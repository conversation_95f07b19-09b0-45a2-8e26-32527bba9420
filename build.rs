use std::io::Result;

fn main() -> Result<()> {
    let libs_path = "libs";
    // 指定动态链接库的搜索路径
    println!("cargo:rustc-link-search=native={}", libs_path);
    println!("cargo:rustc-env=LD_LIBRARY_PATH={}", libs_path);
    // 指定要链接的动态库名称
    println!("cargo:rustc-link-lib=dylib=ZegoExpressEngine");
    println!("cargo:rustc-link-lib=dylib=zego_server_assistant");

    tonic_build::configure()
        .build_client(false)
        .build_server(true)
        .build_transport(true)
        .out_dir("src")
        .type_attribute(".", "#[derive(serde::Serialize,serde::Deserialize)]")
        .compile_protos(&["proto/zego_stream.proto"], &["proto"])?;
    Ok(())
}
