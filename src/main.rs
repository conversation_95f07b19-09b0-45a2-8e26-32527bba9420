extern crate nix;

use ipc_channel::ipc;
use nix::{sys::wait::waitpid, unistd::*};
use serde::Deserialize;
use std::collections::HashMap;
use std::pin::Pin;
use tokio::sync::mpsc;
use tokio_stream::{wrappers::ReceiverStream, Stream, StreamExt};
use tonic::{transport::Server, Request, Response, Status, Streaming};

mod zego_process;
use zego_process::ZegoProcess;

mod zego_stream;
use zego_stream::{
    zego_stream_server::{ZegoStream, ZegoStreamServer},
    EventWrap,
};

fn time_now() -> String {
    chrono::Local::now().format("%Y-%m-%d %H:%M:%S").to_string()
}

/*
#[allow(unused)]
fn match_for_io_error(err_status: &Status) -> Option<&std::io::Error> {
    let mut err: &(dyn Error + 'static) = err_status;

    loop {
        if let Some(io_err) = err.downcast_ref::<std::io::Error>() {
            return Some(io_err);
        }

        // h2::Error do not expose std::io::Error with `source()`
        // https://github.com/hyperium/h2/pull/462
        if let Some(h2_err) = err.downcast_ref::<h2::Error>() {
            if let Some(io_err) = h2_err.get_io() {
                return Some(io_err);
            }
        }

        err = match err.source() {
            Some(err) => err,
            None => return None,
        };
    }
}
*/

#[derive(Default, Debug, Deserialize)]
pub struct ZegoStreamServiceConfig {
    pub grpc_port: Option<u32>,
    pub zego_log: Option<bool>,
    pub zego_secrets: HashMap<u32, String>,
}

#[derive(Default)]
pub struct ZegoStreamService {
    config: ZegoStreamServiceConfig,
}

#[tonic::async_trait]
impl ZegoStream for ZegoStreamService {
    type StreamTransferStream = Pin<Box<dyn Stream<Item = Result<EventWrap, Status>> + Send>>;

    async fn stream_transfer(&self, req: Request<Streaming<EventWrap>>) -> Result<Response<Self::StreamTransferStream>, Status> {
        let (grpc_tx, grpc_rx) = mpsc::channel(16);
        let out_stream = ReceiverStream::new(grpc_rx);
        let mut in_stream = req.into_inner();

        let (stream_recv_tx, stream_recv_rx): (ipc::IpcSender<Option<EventWrap>>, ipc::IpcReceiver<Option<EventWrap>>) = ipc::channel().unwrap();
        let (stream_send_tx, stream_send_rx): (ipc::IpcSender<Option<EventWrap>>, ipc::IpcReceiver<Option<EventWrap>>) = ipc::channel().unwrap();

        // 监听stream_send管道的数据, 发送到grpc数据流输出
        std::thread::spawn(move || {
            async_std::task::block_on(async {
                while let Ok(Some(wrap)) = stream_send_rx.recv() {
                    let _ = grpc_tx.send(Ok(wrap)).await;
                }
                // println!("{} stream_send thread finish", time_now());
            });
        });

        // 监听grpc数据流进入的数据, 发送到stream_recv管道
        // 如果tokio::spawn有异常, 就换用 std::thread::spawn(move || async_std::task::block_on(async {}));
        tokio::spawn(async move {
            while let Some(Ok(wrap)) = in_stream.next().await {
                let _ = stream_recv_tx.send(Some(wrap));
            }
            let _ = stream_recv_tx.send(None);
            // println!("{} stream_recv thread finish", time_now());
        });

        // fork子进程: 接收stream_recv管道的数据作为grpc的输入; 发送grpc的输出到stream_send管道
        match unsafe { fork() } {
            Ok(ForkResult::Child) => {
                println!("{} stream process {} start", time_now(), getpid());
                ZegoProcess::new(stream_recv_rx, stream_send_tx, self.config.zego_secrets.clone(), self.config.zego_log.unwrap_or(false)).start();
                unsafe { libc::_exit(0) };
            }

            Ok(ForkResult::Parent { child, .. }) => {
                tokio::spawn(async move {
                    waitpid(child, None).unwrap();
                    println!("{} stream process {} exit", time_now(), child);
                });
            }

            Err(e) => panic!("fork error: {}", e),
        }

        Ok(Response::new(Box::pin(out_stream) as Self::StreamTransferStream))
    }
}

// #[tokio::main]
#[tokio::main(worker_threads = 800)]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let config: ZegoStreamServiceConfig = if let Ok(config_file) = std::fs::File::open("config.json") {
        if let Ok(config) = serde_json::from_reader(config_file) {
            config
        } else {
            return Err("config.json is invalid json format".into());
        }
    } else {
        return Err("config.json is not found".into());
    };
    let listen_addr = format!("0.0.0.0:{}", config.grpc_port.unwrap_or(8000)).parse().unwrap();
    let server = ZegoStreamService { config };
    println!("{} start server at {}, pid: {}", time_now(), listen_addr, getpid());
    Server::builder().add_service(ZegoStreamServer::new(server)).serve(listen_addr).await?;

    Ok(())
}
