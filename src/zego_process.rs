use ipc_channel::ipc;
use lazy_static::lazy_static;
use nix::unistd::getpid;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::ffi::{c_char, c_void, CStr, CString};
use std::ptr::null_mut;
use std::sync::Mutex;
use std::thread;
use std::time;

mod zego_sdk;
mod zego_token;

use crate::zego_stream::{EventType, EventWrap};

lazy_static! {
    static ref STREAM_SEND: Mutex<Option<ipc::IpcSender<Option<EventWrap>>>> = Mutex::new(None);
    static ref PULL_STREAM: Mutex<Option<String>> = Mutex::new(None);
}

const ZEGO_TOKEN_TTL: i64 = 60 * 60 * 24;
const ZEGO_RENDER_AUDIO_INTERVAL: time::Duration = time::Duration::from_millis(100);

const ZEGO_STREAM_UPDATE_TYPE_ADD: &str = "add";
const ZEGO_STREAM_UPDATE_TYPE_DEL: &str = "del";

fn time_now() -> String {
    chrono::Local::now().format("%Y-%m-%d %H:%M:%S").to_string()
}

fn timestamp_now() -> i64 {
    chrono::Local::now().timestamp()
}

#[derive(Debug, Serialize, Deserialize)]
struct ErrorWrap {
    code: i32,
    msg: String,
}

impl ErrorWrap {
    pub fn new(code: i32, msg: String) -> Self {
        Self { code, msg }
    }
}

pub struct ZegoProcess {
    stream_recv: ipc::IpcReceiver<Option<EventWrap>>,
    // stream_send: ipc::IpcSender<Option<EventWrap>>,
    app_id: u32,
    user_id: String,
    room_id: String,
    stream_id: String,
    secrets: HashMap<u32, String>,
    zego_log: bool,
    audio_frame_param: zego_sdk::ZegoAudioFrameParam,
}

#[derive(Debug, Deserialize)]
struct ZegoLoginRequest {
    app_id: u32,
    user_id: String,
    room_id: String,
    stream_id: String,
}

#[derive(Debug, Deserialize)]
struct ZegoSendTextMessageRequest {
    content: String,
}

#[derive(Debug, Deserialize)]
struct ZegoPullStreamRequest {
    stream_id: String,
}

#[derive(Debug, Deserialize, Serialize)]
struct ZegoStreamUpdateEvent {
    room_id: String,
    update_type: String,
    stream_list: Vec<String>,
    user_list: Vec<String>,
}

impl ZegoProcess {
    pub fn new(stream_recv: ipc::IpcReceiver<Option<EventWrap>>, stream_send: ipc::IpcSender<Option<EventWrap>>, secrets: HashMap<u32, String>, zego_log: bool) -> Self {
        let mut send_guard = STREAM_SEND.lock().unwrap();
        *send_guard = Some(stream_send);

        Self {
            stream_recv,
            // stream_send,
            app_id: 0,
            user_id: String::new(),
            room_id: String::new(),
            stream_id: String::new(),
            secrets,
            zego_log,
            audio_frame_param: zego_sdk::ZegoAudioFrameParam::new(),
        }
    }

    pub fn start(&mut self) {
        while let Ok(recv) = self.stream_recv.recv() {
            if let Some(data) = recv {
                if data.event == EventType::Audio as i32 {
                    self.handle_push_audio_data(data.data);
                } else if data.event == EventType::Login as i32 {
                    self.handle_login_and_publish(data.json);
                } else if self.room_id.is_empty() {
                    return_json_event(EventType::ClientError, ErrorWrap::new(1000002, String::from("room not login")));
                } else if data.event == EventType::SendTextMessage as i32 {
                    self.handle_send_text_message(data.json);
                } else if data.event == EventType::StartPullStream as i32 {
                    self.handle_start_pull_stream(data.json);
                } else if data.event == EventType::StopPullStream as i32 {
                    self.handle_stop_pull_stream(data.json);
                }
            } else {
                self.stop();
                break;
            }
        }
    }

    fn stop(&mut self) {
        // 停止拉流
        let pull_stream_guard: std::sync::MutexGuard<'_, Option<String>> = PULL_STREAM.lock().unwrap();
        if let Some(pull_stream) = pull_stream_guard.as_ref() {
            if !pull_stream.is_empty() {
                let stream_id = CString::new(pull_stream.as_str()).unwrap();
                unsafe { zego_sdk::zego_express_stop_playing_stream(stream_id.as_ptr()) };
            }
        }
        // 停止推流
        if !self.stream_id.is_empty() {
            unsafe { zego_sdk::zego_express_stop_publishing_stream(0) };
        }
        // 退出登录
        if !self.room_id.is_empty() {
            let room_id = CString::new(self.room_id.as_str()).unwrap();
            unsafe { zego_sdk::zego_express_logout_room(room_id.as_ptr()) };
            println!("{} user {} logout room {}", time_now(), self.user_id, self.room_id,);
        }
        // 释放引擎
        unsafe { zego_sdk::zego_express_engine_uninit_async() };
    }

    fn handle_login_and_publish(&mut self, json_str: String) {
        let login_req: ZegoLoginRequest;
        if let Ok(req) = serde_json::from_str(&json_str) {
            login_req = req
        } else {
            return_json_event(EventType::ClientError, ErrorWrap::new(1001091, String::from("json format is invalid, parse failed")));
            return;
        };

        let secret = if let Some(secret) = self.secrets.get(&login_req.app_id) {
            secret
        } else {
            return_json_event(EventType::ClientError, ErrorWrap::new(1001004, String::from("app_id not support")));
            return;
        };
        let log_config = if self.zego_log {
            zego_sdk::ZegoLogConfig::new(&format!("{}_{}", login_req.user_id, timestamp_now()))
        } else {
            zego_sdk::ZegoLogConfig::new("")
        };

        // let log_path_str: String = unsafe { CStr::from_ptr(log_config.log_path.as_ptr()).to_str().unwrap().to_string() };
        println!("{} user {} login room {} at process: {}", time_now(), login_req.user_id, login_req.room_id, getpid());

        unsafe {
            zego_sdk::zego_express_set_log_config(log_config);
            // 初始化引擎
            zego_sdk::zego_express_engine_init_with_profile(zego_sdk::ZegoEngineProfile::new(login_req.app_id));
            // zego_sdk::zego_express_enable_debug_assistant(true); // 开启明文日志
            // 调整音频参数
            zego_sdk::zego_express_enable_aec(false);
            zego_sdk::zego_express_enable_custom_audio_io(true, &mut zego_sdk::ZegoCustomAudioConfig::new(), 0);
            zego_sdk::zego_express_start_audio_data_observer(zego_sdk::OBSERVER_BIT_MASK_ON_PLAYER_AUDIO_DATA, self.audio_frame_param);
            // 注册回调
            zego_sdk::zego_register_room_login_result_callback(zego_on_room_login_result, null_mut());
            zego_sdk::zego_register_room_stream_update_callback(zego_on_room_stream_update, null_mut());
            zego_sdk::zego_register_player_audio_data_callback(zego_on_player_audio_data, null_mut());
        }

        let room_id = CString::new(login_req.room_id.as_str()).unwrap();
        let user_id = CString::new(login_req.user_id.as_str()).unwrap();
        let stream_id = CString::new(login_req.stream_id.as_str()).unwrap();
        let secret = CString::new(secret.as_str()).unwrap();
        let user = zego_sdk::ZegoUser::new(login_req.user_id.as_str(), login_req.user_id.as_str());
        let token_payload = CString::new(format!(r#"{{"room_id":"{}","privilege":{{"1":1,"2":1}},"stream_id_list":null}}"#, login_req.room_id)).unwrap();
        let token_result = unsafe { zego_token::GenerateToken04(login_req.app_id, user_id.as_ptr(), secret.as_ptr(), ZEGO_TOKEN_TTL, token_payload.as_ptr()) };
        let mut room_config = zego_sdk::ZegoRoomConfig::new(unsafe { CStr::from_ptr(token_result.token).to_str().unwrap() });
        let login_ret = unsafe { zego_sdk::zego_express_login_room(room_id.as_ptr(), user, &mut room_config) };
        if login_ret == 0 {
            self.app_id = login_req.app_id;
            self.user_id = login_req.user_id;
            self.room_id = login_req.room_id;
        } else {
            return;
        }

        let publish_ret = unsafe { zego_sdk::zego_express_start_publishing_stream(stream_id.as_ptr(), 0) };
        if publish_ret == 0 {
            self.stream_id = login_req.stream_id;
        } else {
            return;
        }

        // 启动音频渲染线程
        thread::spawn(move || zego_sdk::ZegoRenderAudioThread::new(ZEGO_RENDER_AUDIO_INTERVAL, zego_sdk::ZegoAudioFrameParam::new()).start());
    }

    fn handle_send_text_message(&mut self, json_str: String) {
        let send_text_msg_req: ZegoSendTextMessageRequest;
        if let Ok(req) = serde_json::from_str(&json_str) {
            send_text_msg_req = req
        } else {
            return_json_event(EventType::ClientError, ErrorWrap::new(1001091, String::from("json format is invalid, parse failed")));
            return;
        };

        let room_id = CString::new(self.room_id.as_str()).unwrap();
        let message = CString::new(send_text_msg_req.content.as_str()).unwrap();
        let mut msg_seq = 0;
        unsafe {
            zego_sdk::zego_express_send_broadcast_message(room_id.as_ptr(), message.as_ptr(), &mut msg_seq);
        }
        println!("{} user {} send text message: {}, seq: {}", time_now(), self.user_id, send_text_msg_req.content, msg_seq);
        // return_json_event(EventType::SendTextMessage, msg_seq);
    }

    fn handle_start_pull_stream(&mut self, json_str: String) {
        let mut pull_stream_guard = PULL_STREAM.lock().unwrap();
        if let Some(pull_stream) = pull_stream_guard.as_ref() {
            if !pull_stream.is_empty() {
                return_json_event(EventType::ClientError, ErrorWrap::new(1000000, format!("pulling stream {} now", pull_stream)));
                return;
            }
        }

        let pull_stream_req: ZegoPullStreamRequest;
        if let Ok(req) = serde_json::from_str(&json_str) {
            pull_stream_req = req;
        } else {
            return_json_event(EventType::ClientError, ErrorWrap::new(1000016, String::from("stream_id is invalid")));
            return;
        };

        let stream_id = CString::new(pull_stream_req.stream_id.as_str()).unwrap();
        let start_pull_ret = unsafe { zego_sdk::zego_express_start_playing_stream(stream_id.as_ptr(), null_mut() as *mut zego_sdk::ZegoCanvas) };
        if start_pull_ret == 0 {
            *pull_stream_guard = Some(pull_stream_req.stream_id.clone());
            println!("{} user {} start pull stream: {}", time_now(), self.user_id, pull_stream_req.stream_id);
        } else {
            return_json_event(EventType::ServerError, ErrorWrap::new(1000000, format!("start pull stream failed: code={start_pull_ret}")));
        }
    }

    fn handle_stop_pull_stream(&mut self, json_str: String) {
        let mut pull_stream_guard = PULL_STREAM.lock().unwrap();
        let pull_stream_req: ZegoPullStreamRequest;
        if let Ok(req) = serde_json::from_str(&json_str) {
            pull_stream_req = req;

            if let Some(pull_stream) = pull_stream_guard.as_ref() {
                if *pull_stream != pull_stream_req.stream_id {
                    let err = ErrorWrap::new(1000000, format!("pulling stream {} now but not {}", pull_stream, pull_stream_req.stream_id));
                    return_json_event(EventType::ClientError, err);
                    return;
                }
            }
        } else {
            return_json_event(EventType::ClientError, ErrorWrap::new(1001091, String::from("json format is invalid, parse failed")));
            return;
        };

        let stream_id = CString::new(pull_stream_req.stream_id.as_str()).unwrap();
        let stop_pull_ret = unsafe { zego_sdk::zego_express_stop_playing_stream(stream_id.as_ptr()) };
        if stop_pull_ret == 0 {
            *pull_stream_guard = None;
            println!("{} user {} stop pull stream: {}", time_now(), self.user_id, pull_stream_req.stream_id);
        } else {
            return_json_event(EventType::ServerError, ErrorWrap::new(1000000, format!("stop pull stream failed: code={stop_pull_ret}")));
            return;
        }
    }

    fn handle_push_audio_data(&mut self, data: Vec<u8>) {
        if self.stream_id.is_empty() {
            return_json_event(EventType::ClientError, ErrorWrap::new(1000015, String::from("stream not pushing")));
            return;
        }
        // println!("{} send audio data, len: {}, md5_sum: {}", time_now(), data.len(), format!("{:x}", md5::compute(&data)));
        let send_ret = unsafe { zego_sdk::zego_express_send_custom_audio_capture_pcm_data(data.as_ptr(), data.len() as u32, self.audio_frame_param, 0) };
        if send_ret != 0 {
            println!("{} send audio data failed, ret: {}", time_now(), send_ret);
        }
    }
}

fn return_audio_data(data: Vec<u8>) {
    return_event(EventType::Audio, String::new(), data);
}

fn return_json_event<T: Serialize>(event: EventType, json: T) {
    let json_str = serde_json::to_string(&json).unwrap();
    return_event(event, json_str, Vec::new());
}

fn return_event(event: EventType, json: String, data: Vec<u8>) {
    let wrap = EventWrap { event: event as i32, json, data };

    // 使用全局的stream_send
    if let Some(ref stream_send) = *STREAM_SEND.lock().unwrap() {
        stream_send.send(Some(wrap)).unwrap();
    }
}

extern "C" fn zego_on_room_login_result(error_code: i32, extended_data: *const c_char, _room_id: *const c_char, _seq: u32, _user_context: *mut c_void) {
    if error_code == 0 {
        let json_str = unsafe { CStr::from_ptr(extended_data).to_str().unwrap().to_string() };
        return_event(EventType::LoginResult, json_str, Vec::new());
    } else {
        return_json_event(EventType::LoginResult, ErrorWrap::new(error_code, String::from("login error")));
    }
}

extern "C" fn zego_on_room_stream_update(
    room_id: *const c_char,
    update_type: u32,
    stream_list: *const zego_sdk::ZegoStream,
    stream_info_count: u32,
    _extended_data: *const c_char,
    _user_context: *mut c_void,
) {
    let update_type_str = if update_type == 0 { ZEGO_STREAM_UPDATE_TYPE_ADD } else { ZEGO_STREAM_UPDATE_TYPE_DEL };
    let mut stream_list_vec = Vec::<String>::new();
    let mut user_list_vec = Vec::<String>::new();
    for i in 0..stream_info_count {
        let stream = unsafe { &*(stream_list.offset(i as isize) as *const zego_sdk::ZegoStream) };
        // 把stream.stream_id转化成String
        // let stream_id_str = String::from_utf8_lossy(vec![0; 128].as_slice()).to_string();
        let stream_id = unsafe { CStr::from_ptr(stream.stream_id.as_ptr()).to_str().unwrap().to_string() };
        let user_id = unsafe { CStr::from_ptr(stream.user.user_id.as_ptr()).to_str().unwrap().to_string() };
        stream_list_vec.push(stream_id);
        user_list_vec.push(user_id);
    }
    let stream_update_event = ZegoStreamUpdateEvent {
        room_id: unsafe { CStr::from_ptr(room_id).to_str().unwrap().to_string() },
        update_type: update_type_str.to_string(),
        stream_list: stream_list_vec.clone(),
        user_list: user_list_vec,
    };
    return_json_event(EventType::StreamUpdate, stream_update_event);

    let mut pull_stream_guard = PULL_STREAM.lock().unwrap();
    if update_type_str == ZEGO_STREAM_UPDATE_TYPE_DEL {
        if let Some(pull_stream) = pull_stream_guard.clone().as_ref() {
            for stream_id_str in stream_list_vec.clone() {
                if *pull_stream == stream_id_str {
                    let stream_id = CString::new(stream_id_str.as_str()).unwrap();
                    thread::spawn(move || unsafe { zego_sdk::zego_express_stop_playing_stream(stream_id.as_ptr()) });
                    *pull_stream_guard = None;
                }
            }
        }
    }
}

extern "C" fn zego_on_player_audio_data(data: *const u8, data_length: u32, _param: zego_sdk::ZegoAudioFrameParam, _stream_id: *const c_char, _user_context: *mut c_void) {
    let data_slice = unsafe { std::slice::from_raw_parts(data, data_length as usize) };
    return_audio_data(data_slice.to_vec());
}
