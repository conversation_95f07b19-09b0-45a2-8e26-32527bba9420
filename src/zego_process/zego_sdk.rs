use std::ffi::{c_char, c_void};
use std::thread;
use std::time;

#[allow(unused)]
const ZEGO_SCENARIO_DEFAULT: u32 = 3;
const ZEGO_EXPRESS_MAX_COMMON_LEN: usize = 512;
const ZEGO_EXPRESS_MAX_ROOM_TOKEN_VALUE_LEN: usize = 2048;
const ZEGO_EXPRESS_MAX_USERID_LEN: usize = 64;
const ZEGO_EXPRESS_MAX_USERNAME_LEN: usize = 256;
const ZEGO_EXPRESS_MAX_STREAM_LEN: usize = 256;
const ZEGO_EXPRESS_MAX_EXTRA_INFO_LEN: usize = 1024;
const ZEGO_AUDIO_SOURCE_TYPE_CUSTOM: u32 = 1;
#[allow(unused)]
const ZEGO_VIDEO_BUFFER_TYPE_RAW_DATA: u32 = 1;
const ZEGO_AUDIO_SAMPLE_RATE_16K: u32 = 16000;
const ZEGO_AUDIO_CHANNEL_MONO: u32 = 1;

pub const OBSERVER_BIT_MASK_ON_PLAYER_AUDIO_DATA: u32 = 8;

const ZEGO_LOG_SIZE: u64 = 5 * 1024 * 1024;
const ZEGO_LOG_COUNT: u32 = 3;

pub struct ZegoRenderAudioThread {
    interval: time::Duration, // 等待时长
    buffer_len: u32,          // 缓冲区长度
    keep_running: bool,
    audio_frame_param: ZegoAudioFrameParam,
}

impl ZegoRenderAudioThread {
    pub fn new(interval: time::Duration, audio_frame_param: ZegoAudioFrameParam) -> Self {
        let buffer_len = (audio_frame_param.sample_rate * audio_frame_param.channel * 2) * (interval.as_millis() as u32) / 1000;
        // println!("start zego render audio thread with buffer size: {} and interval: {:?}", buffer_len, interval);
        Self {
            interval,
            buffer_len,
            keep_running: true,
            audio_frame_param,
        }
    }

    pub fn start(&mut self) {
        let mut buffer = vec![0u8; self.buffer_len as usize];
        while self.keep_running {
            unsafe { zego_express_fetch_custom_audio_render_pcm_data(buffer.as_mut_ptr(), self.buffer_len, self.audio_frame_param) };
            thread::sleep(self.interval);
        }
    }

    #[allow(unused)]
    pub fn stop(&mut self) {
        self.keep_running = false;
    }
}

#[repr(C)]
pub struct ZegoEngineProfile {
    pub app_id: u32,
    pub app_sign: [c_char; 64],
    pub scenario: u32,
}

impl ZegoEngineProfile {
    pub fn new(app_id: u32) -> Self {
        Self {
            app_id: app_id,
            app_sign: [0; 64],
            scenario: ZEGO_SCENARIO_DEFAULT,
        }
    }
}

#[repr(C)]
pub struct ZegoLogConfig {
    pub log_path: [c_char; ZEGO_EXPRESS_MAX_COMMON_LEN],
    pub log_size: u64,
    pub log_count: u32,
}

impl ZegoLogConfig {
    pub fn new(log_id: &str) -> Self {
        let log_count = ZEGO_LOG_COUNT;
        let mut log_size = 0;
        let mut log_path = [0; ZEGO_EXPRESS_MAX_COMMON_LEN];
        if log_id.is_empty() {
            "/var/log/zego".as_bytes().iter().zip(log_path.iter_mut()).for_each(|(&byte, place)| *place = byte as c_char);
        } else {
            let log_path_str = format!("/var/log/zego/{}", log_id);
            log_path_str.as_bytes().iter().zip(log_path.iter_mut()).for_each(|(&byte, place)| *place = byte as c_char);
            log_size = ZEGO_LOG_SIZE;
        }
        Self { log_path, log_size, log_count }
    }
}

#[repr(C)]
pub struct ZegoRoomConfig {
    pub max_member_count: u32,
    pub is_user_status_notify: bool,
    pub token: [c_char; ZEGO_EXPRESS_MAX_ROOM_TOKEN_VALUE_LEN],
    pub capability_negotiation_types: u32,
}

impl ZegoRoomConfig {
    pub fn new(token_str: &str) -> Self {
        let mut token = [0; ZEGO_EXPRESS_MAX_ROOM_TOKEN_VALUE_LEN];
        token_str.as_bytes().iter().enumerate().for_each(|(i, &byte)| token[i] = byte as c_char);
        Self {
            max_member_count: 50,
            is_user_status_notify: true,
            token,
            capability_negotiation_types: 0,
        }
    }
}

#[repr(C)]
pub struct ZegoUser {
    pub user_id: [c_char; ZEGO_EXPRESS_MAX_USERID_LEN],
    pub user_name: [c_char; ZEGO_EXPRESS_MAX_USERNAME_LEN],
}

impl ZegoUser {
    pub fn new(user_id_str: &str, user_name_str: &str) -> Self {
        let mut user_id = [0 as c_char; ZEGO_EXPRESS_MAX_USERID_LEN];
        let mut user_name = [0 as c_char; ZEGO_EXPRESS_MAX_USERNAME_LEN];
        user_id_str.as_bytes().iter().enumerate().for_each(|(i, &byte)| user_id[i] = byte as c_char);
        user_name_str.as_bytes().iter().enumerate().for_each(|(i, &byte)| user_name[i] = byte as c_char);
        Self { user_id, user_name }
    }
}

#[repr(C)]
pub struct ZegoCustomAudioConfig {
    pub source_type: u32,
}

impl ZegoCustomAudioConfig {
    pub fn new() -> Self {
        Self {
            source_type: ZEGO_AUDIO_SOURCE_TYPE_CUSTOM,
        }
    }
}

#[allow(unused)]
#[repr(C)]
pub struct ZegoCustomVideoCaptureConfig {
    pub buffer_type: u32,
}

impl ZegoCustomVideoCaptureConfig {
    #[allow(unused)]
    pub fn new() -> Self {
        Self {
            buffer_type: ZEGO_VIDEO_BUFFER_TYPE_RAW_DATA,
        }
    }
}

#[repr(C)]
#[derive(Clone, Copy)]
pub struct ZegoAudioFrameParam {
    pub sample_rate: u32,
    pub channel: u32,
}

impl ZegoAudioFrameParam {
    pub fn new() -> Self {
        Self {
            sample_rate: ZEGO_AUDIO_SAMPLE_RATE_16K,
            channel: ZEGO_AUDIO_CHANNEL_MONO,
        }
    }
}

#[repr(C)]
pub struct ZegoCanvas {
    pub view: *mut c_void,
    pub view_mode: u32, // enum ZegoViewMode
    pub background_color: i32,
    pub alpha_blend: bool,
}

#[repr(C)]
pub struct ZegoStream {
    pub user: ZegoUser,
    pub stream_id: [c_char; ZEGO_EXPRESS_MAX_STREAM_LEN],
    pub extra_info: [c_char; ZEGO_EXPRESS_MAX_EXTRA_INFO_LEN],
}

// 登录房间结果回调方法
// typedef void (*zego_on_room_login_result)(zego_error error_code, const char *extended_data, const char *room_id, zego_seq seq, void *user_context);
pub type ZegoOnRoomLoginResultCallback = extern "C" fn(error_code: i32, extended_data: *const c_char, room_id: *const c_char, seq: u32, user_context: *mut c_void);

// 流更新回调方法
// typedef void (*zego_on_room_stream_update)(const char *room_id, enum zego_update_type update_type, const struct zego_stream *stream_list, unsigned int stream_info_count, const char *extended_data, void *user_context);
pub type ZegoOnRoomStreamUpdateCallback =
    extern "C" fn(room_id: *const c_char, update_type: u32, stream_list: *const ZegoStream, stream_info_count: u32, extended_data: *const c_char, user_context: *mut c_void);

// 播放音频数据回调方法
// typedef void (*zego_on_player_audio_data)(const unsigned char *data, unsigned int data_length, struct zego_audio_frame_param param, const char *stream_id, void *user_context);
pub type ZegoOnPlayerAudioDataCallback = extern "C" fn(data: *const u8, data_length: u32, param: ZegoAudioFrameParam, stream_id: *const c_char, user_context: *mut c_void);

#[link(name = "ZegoExpressEngine", kind = "dylib")]
extern "C" {
    // zego_express_get_version(const char **version); 获取版本号
    #[allow(unused)]
    pub fn zego_express_get_version(version: *mut *const c_char);

    // zego_express_engine_init_with_profile(struct zego_engine_profile profile); 初始化引擎
    pub fn zego_express_engine_init_with_profile(profile: ZegoEngineProfile);

    // zego_express_set_log_config(struct zego_log_config config); 设置日志配置
    pub fn zego_express_set_log_config(config: ZegoLogConfig);

    // zego_express_enable_debug_assistant(bool enable)
    #[allow(unused)]
    pub fn zego_express_enable_debug_assistant(enable: bool);

    // zego_express_login_room(const char *room_id, struct zego_user user, struct zego_room_config *config); 登录房间
    pub fn zego_express_login_room(room_id: *const c_char, user: ZegoUser, config: *mut ZegoRoomConfig) -> i32;

    // zego_express_start_publishing_stream(const char *stream_id, enum zego_publish_channel channel); 开始发布流
    pub fn zego_express_start_publishing_stream(stream_id: *const c_char, channel: u32) -> i32;

    // zego_express_enable_aec(bool enable); 开启回声消除
    pub fn zego_express_enable_aec(enable: bool) -> i32;

    //  zego_express_enable_custom_audio_io(bool enable, struct zego_custom_audio_config *config, enum zego_publish_channel channel); 开启自定义音频IO
    pub fn zego_express_enable_custom_audio_io(enable: bool, config: *mut ZegoCustomAudioConfig, channel: u32) -> i32;

    // zego_express_enable_custom_video_capture(bool enable, struct zego_custom_video_capture_config *config, enum zego_publish_channel channel); 开启自定义视频采集
    #[allow(unused)]
    pub fn zego_express_enable_custom_video_capture(enable: bool, config: *mut ZegoCustomVideoCaptureConfig, channel: u32) -> i32;

    //  zego_express_start_audio_data_observer(unsigned int observer_bit_mask, struct zego_audio_frame_param param); 开始音频数据观察
    pub fn zego_express_start_audio_data_observer(observer_bit_mask: u32, param: ZegoAudioFrameParam) -> i32;

    // zego_express_send_broadcast_message(const char *room_id, const char *message, zego_seq *sequence); 发送广播消息
    pub fn zego_express_send_broadcast_message(room_id: *const c_char, message: *const c_char, sequence: *mut i32) -> i32;

    // zego_express_start_playing_stream(const char *stream_id, struct zego_canvas *canvas); 开始拉流
    pub fn zego_express_start_playing_stream(stream_id: *const c_char, canvas: *mut ZegoCanvas) -> i32;

    // zego_express_stop_playing_stream(const char *stream_id); 停止拉流
    pub fn zego_express_stop_playing_stream(stream_id: *const c_char) -> i32;

    // zego_express_send_custom_audio_capture_pcm_data(unsigned char *data, unsigned int data_length, struct zego_audio_frame_param param, enum zego_publish_channel channel); 发送音频的pcm数据
    pub fn zego_express_send_custom_audio_capture_pcm_data(data: *const u8, data_length: u32, param: ZegoAudioFrameParam, channel: u32) -> i32;

    // zego_express_fetch_custom_audio_render_pcm_data(unsigned char *data, unsigned int data_length, struct zego_audio_frame_param param); 音频的pcm数据解码
    pub fn zego_express_fetch_custom_audio_render_pcm_data(data: *mut u8, data_length: u32, param: ZegoAudioFrameParam) -> i32;

    // 退出程序
    // zego_express_stop_publishing_stream(enum zego_publish_channel channel); 停止发布流
    pub fn zego_express_stop_publishing_stream(channel: u32) -> i32;

    // zego_express_engine_uninit_async(void); 释放引擎
    pub fn zego_express_engine_uninit_async() -> i32;

    // zego_express_logout_room(const char *room_id); 退出房间
    pub fn zego_express_logout_room(room_id: *const c_char) -> i32;

    // 回调函数
    // zego_register_room_login_result_callback(zego_on_room_login_result callback_func, void *user_context); 注册登录房间结果回调
    pub fn zego_register_room_login_result_callback(callback_func: ZegoOnRoomLoginResultCallback, user_context: *mut c_void);

    // zego_register_room_stream_update_callback(zego_on_room_stream_update callback_func, void *user_context); 注册流更新回调
    pub fn zego_register_room_stream_update_callback(callback_func: ZegoOnRoomStreamUpdateCallback, user_context: *mut c_void);

    // zego_register_player_audio_data_callback(zego_on_player_audio_data callback_func, void *user_context); 注册播放音频数据回调
    pub fn zego_register_player_audio_data_callback(callback_func: ZegoOnPlayerAudioDataCallback, user_context: *mut c_void);

}
