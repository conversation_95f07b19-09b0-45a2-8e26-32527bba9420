use std::ffi::*;

#[allow(unused)]
#[repr(C)]
pub enum ErrorCode {
    Success = 0,                       // 获取鉴权 token 成功
    AppIDInvalid = 1,                  // 调用方法时传入 appID 参数错误
    UserIDInvalid = 3,                 // 调用方法时传入 userID 参数错误
    SecretInvalid = 5,                 // 调用方法时传入 secret 参数错误
    EffectiveTimeInSecondsInvalid = 6, // 调用方法时传入 effectiveTimeInSeconds 参数错误
}

#[repr(C)]
pub struct ErrorInfo {
    pub error_code: ErrorCode,        // 错误码来自 ErrorCode
    pub error_message: *const c_char, // 使用指针替代 String
}

#[repr(C)]
pub struct ZegoToken04Result {
    pub token: *const c_char, // 使用指针替代 String
    pub error_info: ErrorInfo,
}

#[link(name = "zego_server_assistant", kind = "dylib")]
extern "C" {
    pub fn GenerateToken04(appID: u32, userID: *const c_char, secret: *const c_char, effectiveTimeInSeconds: i64, payload: *const c_char) -> ZegoToken04Result;
}
