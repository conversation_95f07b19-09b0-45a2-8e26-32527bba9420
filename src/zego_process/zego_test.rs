use serde::{Deserialize, Serialize};
use std::ffi::*;
use std::ptr::null_mut;
use std::thread;
use std::time;

pub mod zego_sdk;
pub mod zego_token;

const ZEGO_TOKEN_TTL: i64 = 3600 * 24;
const ZEGO_RENDER_AUDIO_INTERVAL: time::Duration = time::Duration::from_millis(100);

const ZEGO_STREAM_UPDATE_TYPE_ADD: &str = "add";
const ZEGO_STREAM_UPDATE_TYPE_DEL: &str = "del";

fn time_now() -> String {
    chrono::Local::now().format("%Y-%m-%d %H:%M:%S").to_string()
}

fn time_now_ms() -> String {
    chrono::Local::now().format("%Y-%m-%d %H:%M:%S.%f").to_string()
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ZegoConfig {
    pub app_id: u32,
    pub room_id: String,
    pub user_id: String,
    pub stream_id: String,
    pub secret: String,
}

fn main() {
    // 读取config.json文件
    let config_path = "config.json";
    let config_str = if let Ok(config_str) = std::fs::read_to_string(config_path) {
        config_str
    } else {
        println!("read config file failed, path: {}", config_path);
        std::process::exit(1);
    };
    let config: ZegoConfig = if let Ok(config) = serde_json::from_str(&config_str) {
        config
    } else {
        println!("parse config file failed, path: {}", config_path);
        std::process::exit(1);
    };
    println!("zego config: {:?}", config);

    let req_app_id = config.app_id;
    let req_room_id = config.room_id;
    let req_user_id = config.user_id;
    let req_stream_id = config.stream_id;

    let secret = config.secret;
    let log_config = zego_sdk::ZegoLogConfig::new(&format!("{}_{}", req_user_id, chrono::Local::now().timestamp()));

    let log_path_str: String = unsafe { CStr::from_ptr(log_config.log_path.as_ptr()).to_str().unwrap().to_string() };
    println!("user {} login room {} , log to: {}", req_user_id, req_room_id, log_path_str);

    let profile = zego_sdk::ZegoEngineProfile::new(req_app_id);
    let audio_frame_param = zego_sdk::ZegoAudioFrameParam::new();
    let mut custom_audio_config = zego_sdk::ZegoCustomAudioConfig::new();
    // let mut custom_video_config = zego_sdk::ZegoCustomVideoCaptureConfig::new();
    unsafe {
        zego_sdk::zego_express_set_log_config(log_config);
        // 初始化引擎
        zego_sdk::zego_express_engine_init_with_profile(profile);
        // 调整音频参数
        zego_sdk::zego_express_enable_aec(false);
        zego_sdk::zego_express_enable_custom_audio_io(true, &mut custom_audio_config, 0);
        // zego_sdk::zego_express_enable_custom_video_capture(true, &mut custom_video_config, 0);
        zego_sdk::zego_express_start_audio_data_observer(8, audio_frame_param);
        // 注册回调
        zego_sdk::zego_register_room_login_result_callback(zego_on_room_login_result, null_mut());
        zego_sdk::zego_register_room_stream_update_callback(zego_on_room_stream_update, null_mut());
        zego_sdk::zego_register_player_audio_data_callback(zego_on_player_audio_data, null_mut());
        // 启动音频渲染线程
        thread::spawn(move || {
            let audio_frame_param = zego_sdk::ZegoAudioFrameParam::new();
            zego_sdk::ZegoRenderAudioThread::new(ZEGO_RENDER_AUDIO_INTERVAL, audio_frame_param).start();
        });
    }

    let room_id = CString::new(req_room_id.as_str()).unwrap();
    let user_id = CString::new(req_user_id.as_str()).unwrap();
    let stream_id = CString::new(req_stream_id.as_str()).unwrap();
    let secret = CString::new(secret.as_str()).unwrap();
    let user = zego_sdk::ZegoUser::new(req_user_id.as_str(), req_user_id.as_str());
    let token_payload = CString::new(format!(r#"{{"room_id":"{}","privilege":{{"1":1,"2":1}},"stream_id_list":null}}"#, req_room_id)).unwrap();
    let token_result = unsafe { zego_token::GenerateToken04(req_app_id, user_id.as_ptr(), secret.as_ptr(), ZEGO_TOKEN_TTL, token_payload.as_ptr()) };
    let mut room_config = zego_sdk::ZegoRoomConfig::new(unsafe { CStr::from_ptr(token_result.token).to_str().unwrap() });

    println!("{} user {} login room {}", time_now(), req_user_id, req_room_id);
    unsafe { zego_sdk::zego_express_login_room(room_id.as_ptr(), user, &mut room_config) };

    thread::sleep(time::Duration::from_secs(5));

    let publish_ret = unsafe { zego_sdk::zego_express_start_publishing_stream(stream_id.as_ptr(), 0) };
    if publish_ret == 0 {
        println!("{} user {} start publish stream {} success", time_now(), req_user_id, req_stream_id);
    } else {
        println!("{} user {} start publish stream {} failed, ret: {}", time_now(), req_user_id, req_stream_id, publish_ret);
        std::process::exit(2);
    }

    thread::sleep(time::Duration::from_secs(5));

    // 读取test.wav文件
    let file_path = "test.wav";
    let file_data = std::fs::read(file_path).unwrap();
    // pcm数据是去除wav文件开头的44个字节
    let pcm_data = file_data.as_slice()[44..].to_vec();
    let send_batch_size = 16000 * 2 / 100; // 10ms
    let send_interval = time::Duration::from_millis(10);
    let mut send_index = 0;
    let audio_frame_param = zego_sdk::ZegoAudioFrameParam::new();

    println!("{} stream {} send audio data len: {}", time_now(), req_stream_id, pcm_data.len());
    while send_index < pcm_data.len() {
        let mut end = send_index + send_batch_size;
        if end > pcm_data.len() {
            end = pcm_data.len();
        }
        let data = pcm_data[send_index..end].to_vec();
        send_index += send_batch_size;
        println!("{} send data len: {}", time_now_ms(), data.len());
        unsafe { zego_sdk::zego_express_send_custom_audio_capture_pcm_data(data.as_ptr(), data.len() as u32, audio_frame_param, 0) };
        thread::sleep(send_interval);
    }
    println!("{} stream {} send audio data finish", time_now_ms(), req_stream_id);
    thread::sleep(time::Duration::from_secs(5));
    unsafe {
        zego_sdk::zego_express_stop_publishing_stream(0);
        zego_sdk::zego_express_logout_room(room_id.as_ptr());
        zego_sdk::zego_express_engine_uninit_async();
    };
    thread::sleep(time::Duration::from_secs(1));
}

extern "C" fn zego_on_room_login_result(error_code: i32, extended_data: *const c_char, _room_id: *const c_char, _seq: u32, _user_context: *mut c_void) {
    if error_code == 0 {
        let json_str = unsafe { CStr::from_ptr(extended_data).to_str().unwrap().to_string() };
        println!("{} login success: {}", time_now(), json_str);
    } else {
        println!("{} login error: {}", time_now(), error_code);
        std::process::exit(1);
    }
}

extern "C" fn zego_on_room_stream_update(
    room_id: *const c_char,
    update_type: u32,
    stream_list: *const zego_sdk::ZegoStream,
    stream_info_count: u32,
    _extended_data: *const c_char,
    _user_context: *mut c_void,
) {
    let update_type_str = if update_type == 0 { ZEGO_STREAM_UPDATE_TYPE_ADD } else { ZEGO_STREAM_UPDATE_TYPE_DEL };
    for i in 0..stream_info_count {
        let stream = unsafe { &*(stream_list.offset(i as isize) as *const zego_sdk::ZegoStream) };
        let stream_id_raw = stream.stream_id.as_ptr();
        unsafe { zego_sdk::zego_express_start_playing_stream(stream_id_raw, null_mut() as *mut zego_sdk::ZegoCanvas) };
        let stream_id = unsafe { CStr::from_ptr(stream_id_raw).to_str().unwrap().to_string() };
        println!("{} start pull stream: {}", time_now(), stream_id);
    }
    let room_id_str = unsafe { CStr::from_ptr(room_id).to_str().unwrap().to_string() };
    println!("{} room {} stream update: {}", time_now(), room_id_str, update_type_str);
}

extern "C" fn zego_on_player_audio_data(data: *const u8, data_length: u32, _param: zego_sdk::ZegoAudioFrameParam, _stream_id: *const c_char, _user_context: *mut c_void) {
    println!("{} recv audio len: {}", time_now(), data_length);
    let data_slice = unsafe { std::slice::from_raw_parts(data, data_length as usize) };
    println!("{} recv audio data: {:?}", time_now(), data_slice);
}
