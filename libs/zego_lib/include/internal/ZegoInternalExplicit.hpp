
#ifndef __ZEGOEXPRESSEXPLICIT_HPP__
#define __ZEGOEXPRESSEXPLICIT_HPP__

#ifdef ZEGOEXP_EXPLICIT

#ifdef _WIN32
#include <windows.h>
#else
#include <dlfcn.h>
#endif

#define ZEGOEXP_DECLARE_FUNC_PTR(type, ptr) type ptr = nullptr;
#ifdef _WIN32
#define ZEGOEXP_LOAD_LIBRARY()                                                                     \
    handle = (void *)::LoadLibraryA(full_path.c_str());                                            \
    if (nullptr == handle) {                                                                       \
        return ZEGO_ERRCODE_COMMON_LOAD_LIBRARY_FAILED;                                            \
    }

#define ZEGOEXP_LOAD_FUNC_PTR(type, ptr)                                                           \
    ptr = (type)::GetProcAddress((HMODULE)handle, #ptr);                                           \
    if (ptr == nullptr) {                                                                          \
        unLoadLibraryInternal();                                                                   \
        return ZEGO_ERRCODE_COMMON_LOAD_LIBRARY_FUNC_NOT_FOUND;                                    \
    }

#define ZEGOEXP_FREE_LIBRARY() ::FreeLibrary((HMODULE)handle);
#else
#define ZEGOEXP_LOAD_LIBRARY()                                                                     \
    handle = (void *)dlopen(full_path.c_str(), RTLD_LAZY);                                         \
    if (nullptr == handle) {                                                                       \
        return ZEGO_ERRCODE_COMMON_LOAD_LIBRARY_FAILED;                                            \
    }

#define ZEGOEXP_LOAD_FUNC_PTR(type, ptr)                                                           \
    ptr = (type)dlsym(handle, #ptr);                                                               \
    if (ptr == nullptr) {                                                                          \
        unLoadLibraryInternal();                                                                   \
        return ZEGO_ERRCODE_COMMON_LOAD_LIBRARY_FUNC_NOT_FOUND;                                    \
    }

#define ZEGOEXP_FREE_LIBRARY() dlclose(handle);

#endif

#define ZEGOEXP_DECLARE_FUNC                                                                        \
    void *handle = nullptr;                                                                         \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_add_publish_cdn_url,                                   \
                             zego_express_add_publish_cdn_url)                                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_add_publish_cdn_url_v2,                                \
                             zego_express_add_publish_cdn_url_v2)                                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_ai_voice_changer_get_speaker_list,                     \
                             zego_express_ai_voice_changer_get_speaker_list)                        \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_ai_voice_changer_init,                                 \
                             zego_express_ai_voice_changer_init)                                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_ai_voice_changer_set_speaker,                          \
                             zego_express_ai_voice_changer_set_speaker)                             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_ai_voice_changer_update,                               \
                             zego_express_ai_voice_changer_update)                                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_audio_effect_player_get_current_progress,              \
                             zego_express_audio_effect_player_get_current_progress)                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_audio_effect_player_get_total_duration,                \
                             zego_express_audio_effect_player_get_total_duration)                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_audio_effect_player_load_resource,                     \
                             zego_express_audio_effect_player_load_resource)                        \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_audio_effect_player_pause,                             \
                             zego_express_audio_effect_player_pause)                                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_audio_effect_player_pause_all,                         \
                             zego_express_audio_effect_player_pause_all)                            \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_audio_effect_player_resume,                            \
                             zego_express_audio_effect_player_resume)                               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_audio_effect_player_resume_all,                        \
                             zego_express_audio_effect_player_resume_all)                           \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_audio_effect_player_seek_to,                           \
                             zego_express_audio_effect_player_seek_to)                              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_audio_effect_player_set_play_speed,                    \
                             zego_express_audio_effect_player_set_play_speed)                       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_audio_effect_player_set_play_volume,                   \
                             zego_express_audio_effect_player_set_play_volume)                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_audio_effect_player_set_play_volume_all,               \
                             zego_express_audio_effect_player_set_play_volume_all)                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_audio_effect_player_set_publish_volume,                \
                             zego_express_audio_effect_player_set_publish_volume)                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_audio_effect_player_set_publish_volume_all,            \
                             zego_express_audio_effect_player_set_publish_volume_all)               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_audio_effect_player_set_volume,                        \
                             zego_express_audio_effect_player_set_volume)                           \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_audio_effect_player_set_volume_all,                    \
                             zego_express_audio_effect_player_set_volume_all)                       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_audio_effect_player_start,                             \
                             zego_express_audio_effect_player_start)                                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_audio_effect_player_stop,                              \
                             zego_express_audio_effect_player_stop)                                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_audio_effect_player_stop_all,                          \
                             zego_express_audio_effect_player_stop_all)                             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_audio_effect_player_unload_resource,                   \
                             zego_express_audio_effect_player_unload_resource)                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_audio_effect_player_update_position,                   \
                             zego_express_audio_effect_player_update_position)                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_audio_vad_client_reset,                                \
                             zego_express_audio_vad_client_reset)                                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_audio_vad_client_update,                               \
                             zego_express_audio_vad_client_update)                                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_call_experimental_api,                                 \
                             zego_express_call_experimental_api)                                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_copyrighted_music_cancel_download,                     \
                             zego_express_copyrighted_music_cancel_download)                        \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_copyrighted_music_clear_cache,                         \
                             zego_express_copyrighted_music_clear_cache)                            \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_copyrighted_music_download,                            \
                             zego_express_copyrighted_music_download)                               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_copyrighted_music_get_average_score,                   \
                             zego_express_copyrighted_music_get_average_score)                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_copyrighted_music_get_cache_size,                      \
                             zego_express_copyrighted_music_get_cache_size)                         \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_copyrighted_music_get_current_pitch,                   \
                             zego_express_copyrighted_music_get_current_pitch)                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_copyrighted_music_get_duration,                        \
                             zego_express_copyrighted_music_get_duration)                           \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_copyrighted_music_get_full_score,                      \
                             zego_express_copyrighted_music_get_full_score)                         \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_copyrighted_music_get_krc_lyric_by_token,              \
                             zego_express_copyrighted_music_get_krc_lyric_by_token)                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_copyrighted_music_get_lrc_lyric,                       \
                             zego_express_copyrighted_music_get_lrc_lyric)                          \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_copyrighted_music_get_lrc_lyric_with_config,           \
                             zego_express_copyrighted_music_get_lrc_lyric_with_config)              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_copyrighted_music_get_lrc_lyric_with_vendor,           \
                             zego_express_copyrighted_music_get_lrc_lyric_with_vendor)              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_copyrighted_music_get_music_by_token,                  \
                             zego_express_copyrighted_music_get_music_by_token)                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_copyrighted_music_get_previous_score,                  \
                             zego_express_copyrighted_music_get_previous_score)                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_copyrighted_music_get_shared_resource,                 \
                             zego_express_copyrighted_music_get_shared_resource)                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_copyrighted_music_get_shared_resource_v2,              \
                             zego_express_copyrighted_music_get_shared_resource_v2)                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_copyrighted_music_get_standard_pitch,                  \
                             zego_express_copyrighted_music_get_standard_pitch)                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_copyrighted_music_get_total_score,                     \
                             zego_express_copyrighted_music_get_total_score)                        \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_copyrighted_music_init,                                \
                             zego_express_copyrighted_music_init)                                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_copyrighted_music_pause_score,                         \
                             zego_express_copyrighted_music_pause_score)                            \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_copyrighted_music_query_cache,                         \
                             zego_express_copyrighted_music_query_cache)                            \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_copyrighted_music_query_cache_with_config,             \
                             zego_express_copyrighted_music_query_cache_with_config)                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_copyrighted_music_query_cache_with_config_v2,          \
                             zego_express_copyrighted_music_query_cache_with_config_v2)             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_copyrighted_music_query_cache_with_vendor,             \
                             zego_express_copyrighted_music_query_cache_with_vendor)                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_copyrighted_music_request_accompaniment,               \
                             zego_express_copyrighted_music_request_accompaniment)                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_copyrighted_music_request_accompaniment_clip,          \
                             zego_express_copyrighted_music_request_accompaniment_clip)             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_copyrighted_music_request_resource,                    \
                             zego_express_copyrighted_music_request_resource)                       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_copyrighted_music_request_resource_v2,                 \
                             zego_express_copyrighted_music_request_resource_v2)                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_copyrighted_music_request_song,                        \
                             zego_express_copyrighted_music_request_song)                           \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_copyrighted_music_reset_score,                         \
                             zego_express_copyrighted_music_reset_score)                            \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_copyrighted_music_resume_score,                        \
                             zego_express_copyrighted_music_resume_score)                           \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_copyrighted_music_send_extended_request,               \
                             zego_express_copyrighted_music_send_extended_request)                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_copyrighted_music_set_scoring_level,                   \
                             zego_express_copyrighted_music_set_scoring_level)                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_copyrighted_music_start_score,                         \
                             zego_express_copyrighted_music_start_score)                            \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_copyrighted_music_stop_score,                          \
                             zego_express_copyrighted_music_stop_score)                             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_create_ai_voice_changer,                               \
                             zego_express_create_ai_voice_changer)                                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_create_audio_effect_player,                            \
                             zego_express_create_audio_effect_player)                               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_create_audio_vad_client,                               \
                             zego_express_create_audio_vad_client)                                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_create_copyrighted_music,                              \
                             zego_express_create_copyrighted_music)                                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_create_media_data_publisher,                           \
                             zego_express_create_media_data_publisher)                              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_create_media_player,                                   \
                             zego_express_create_media_player)                                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_create_range_audio, zego_express_create_range_audio)   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_create_range_scene, zego_express_create_range_scene)   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_create_real_time_sequential_data_manager,              \
                             zego_express_create_real_time_sequential_data_manager)                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_create_screen_capture_source,                          \
                             zego_express_create_screen_capture_source)                             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_custom_log, zego_express_custom_log)                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_destroy_ai_voice_changer,                              \
                             zego_express_destroy_ai_voice_changer)                                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_destroy_audio_effect_player,                           \
                             zego_express_destroy_audio_effect_player)                              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_destroy_audio_vad_client,                              \
                             zego_express_destroy_audio_vad_client)                                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_destroy_copyrighted_music,                             \
                             zego_express_destroy_copyrighted_music)                                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_destroy_media_data_publisher,                          \
                             zego_express_destroy_media_data_publisher)                             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_destroy_media_player,                                  \
                             zego_express_destroy_media_player)                                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_destroy_range_audio,                                   \
                             zego_express_destroy_range_audio)                                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_destroy_range_scene,                                   \
                             zego_express_destroy_range_scene)                                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_destroy_real_time_sequential_data_manager,             \
                             zego_express_destroy_real_time_sequential_data_manager)                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_destroy_screen_capture_source,                         \
                             zego_express_destroy_screen_capture_source)                            \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_aec, zego_express_enable_aec)                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_agc, zego_express_enable_agc)                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_aligned_audio_aux_data,                         \
                             zego_express_enable_aligned_audio_aux_data)                            \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_alpha_channel_video_encoder,                    \
                             zego_express_enable_alpha_channel_video_encoder)                       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_ans, zego_express_enable_ans)                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_audio_capture_device,                           \
                             zego_express_enable_audio_capture_device)                              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_audio_mixing,                                   \
                             zego_express_enable_audio_mixing)                                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_beautify, zego_express_enable_beautify)         \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_before_audio_prep_audio_data,                   \
                             zego_express_enable_before_audio_prep_audio_data)                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_camera, zego_express_enable_camera)             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_camera_adaptive_fps,                            \
                             zego_express_enable_camera_adaptive_fps)                               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_captured_video_custom_video_render,             \
                             zego_express_enable_captured_video_custom_video_render)                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_check_poc, zego_express_enable_check_poc)       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_color_enhancement,                              \
                             zego_express_enable_color_enhancement)                                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_custom_audio_capture_processing,                \
                             zego_express_enable_custom_audio_capture_processing)                   \
    ZEGOEXP_DECLARE_FUNC_PTR(                                                                       \
        pfnzego_express_enable_custom_audio_capture_processing_after_headphone_monitor,             \
        zego_express_enable_custom_audio_capture_processing_after_headphone_monitor)                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_custom_audio_io,                                \
                             zego_express_enable_custom_audio_io)                                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_custom_audio_playback_processing,               \
                             zego_express_enable_custom_audio_playback_processing)                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_custom_audio_remote_processing,                 \
                             zego_express_enable_custom_audio_remote_processing)                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_custom_video_capture,                           \
                             zego_express_enable_custom_video_capture)                              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_custom_video_processing,                        \
                             zego_express_enable_custom_video_processing)                           \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_custom_video_render,                            \
                             zego_express_enable_custom_video_render)                               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_debug_assistant,                                \
                             zego_express_enable_debug_assistant)                                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_effects_beauty,                                 \
                             zego_express_enable_effects_beauty)                                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_h_265_encode_fallback,                          \
                             zego_express_enable_h_265_encode_fallback)                             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_hardware_decoder,                               \
                             zego_express_enable_hardware_decoder)                                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_hardware_encoder,                               \
                             zego_express_enable_hardware_encoder)                                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_headphone_aec,                                  \
                             zego_express_enable_headphone_aec)                                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_headphone_monitor,                              \
                             zego_express_enable_headphone_monitor)                                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_mix_engine_playout,                             \
                             zego_express_enable_mix_engine_playout)                                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_mix_system_playout,                             \
                             zego_express_enable_mix_system_playout)                                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_play_stream_virtual_stereo,                     \
                             zego_express_enable_play_stream_virtual_stereo)                        \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_publish_direct_to_cdn,                          \
                             zego_express_enable_publish_direct_to_cdn)                             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_remote_video_custom_video_render,               \
                             zego_express_enable_remote_video_custom_video_render)                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_speech_enhance,                                 \
                             zego_express_enable_speech_enhance)                                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_traffic_control,                                \
                             zego_express_enable_traffic_control)                                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_traffic_control_by_channel,                     \
                             zego_express_enable_traffic_control_by_channel)                        \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_transient_ans,                                  \
                             zego_express_enable_transient_ans)                                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_video_object_segmentation,                      \
                             zego_express_enable_video_object_segmentation)                         \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_video_object_segmentation_with_config,          \
                             zego_express_enable_video_object_segmentation_with_config)             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_video_super_resolution,                         \
                             zego_express_enable_video_super_resolution)                            \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_enable_virtual_stereo,                                 \
                             zego_express_enable_virtual_stereo)                                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_engine_init, zego_express_engine_init)                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_engine_init_with_profile,                              \
                             zego_express_engine_init_with_profile)                                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_engine_uninit_async,                                   \
                             zego_express_engine_uninit_async)                                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_fetch_custom_audio_render_pcm_data,                    \
                             zego_express_fetch_custom_audio_render_pcm_data)                       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_free_audio_device_list,                                \
                             zego_express_free_audio_device_list)                                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_free_call_experimental_api_result,                     \
                             zego_express_free_call_experimental_api_result)                        \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_free_room_stream_list,                                 \
                             zego_express_free_room_stream_list)                                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_free_screen_capture_source_list,                       \
                             zego_express_free_screen_capture_source_list)                          \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_free_video_device_list,                                \
                             zego_express_free_video_device_list)                                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_get_android_context,                                   \
                             zego_express_get_android_context)                                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_get_audio_config, zego_express_get_audio_config)       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_get_audio_config_by_channel,                           \
                             zego_express_get_audio_config_by_channel)                              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_get_audio_device_list,                                 \
                             zego_express_get_audio_device_list)                                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_get_audio_device_volume,                               \
                             zego_express_get_audio_device_volume)                                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_get_audio_route_type,                                  \
                             zego_express_get_audio_route_type)                                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_get_camera_max_zoom_factor,                            \
                             zego_express_get_camera_max_zoom_factor)                               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_get_current_audio_device,                              \
                             zego_express_get_current_audio_device)                                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_get_custom_video_capture_surface_texture,              \
                             zego_express_get_custom_video_capture_surface_texture)                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_get_custom_video_process_output_surface_texture,       \
                             zego_express_get_custom_video_process_output_surface_texture)          \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_get_default_audio_device_id,                           \
                             zego_express_get_default_audio_device_id)                              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_get_default_video_device_id,                           \
                             zego_express_get_default_video_device_id)                              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_get_network_time_info,                                 \
                             zego_express_get_network_time_info)                                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_get_room_stream_list,                                  \
                             zego_express_get_room_stream_list)                                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_get_screen_capture_sources,                            \
                             zego_express_get_screen_capture_sources)                               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_get_speaker_volume_in_app,                             \
                             zego_express_get_speaker_volume_in_app)                                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_get_unity_captured_texture,                            \
                             zego_express_get_unity_captured_texture)                               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_get_unity_played_texture,                              \
                             zego_express_get_unity_played_texture)                                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_get_version, zego_express_get_version)                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_get_video_config, zego_express_get_video_config)       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_get_video_device_list,                                 \
                             zego_express_get_video_device_list)                                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_handle_api_call_result,                                \
                             zego_express_handle_api_call_result)                                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_init_video_super_resolution,                           \
                             zego_express_init_video_super_resolution)                              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_is_ai_voice_changer_supported,                         \
                             zego_express_is_ai_voice_changer_supported)                            \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_is_audio_device_muted,                                 \
                             zego_express_is_audio_device_muted)                                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_is_camera_focus_supported,                             \
                             zego_express_is_camera_focus_supported)                                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_is_feature_supported,                                  \
                             zego_express_is_feature_supported)                                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_is_microphone_muted,                                   \
                             zego_express_is_microphone_muted)                                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_is_speaker_muted, zego_express_is_speaker_muted)       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_is_video_decoder_supported,                            \
                             zego_express_is_video_decoder_supported)                               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_is_video_encoder_supported,                            \
                             zego_express_is_video_encoder_supported)                               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_login_room, zego_express_login_room)                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_login_room_with_callback,                              \
                             zego_express_login_room_with_callback)                                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_logout_all_room, zego_express_logout_all_room)         \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_logout_all_room_with_callback,                         \
                             zego_express_logout_all_room_with_callback)                            \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_logout_room, zego_express_logout_room)                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_logout_room_with_callback,                             \
                             zego_express_logout_room_with_callback)                                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_data_publisher_add_media_file_path,              \
                             zego_express_media_data_publisher_add_media_file_path)                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_data_publisher_get_current_duration,             \
                             zego_express_media_data_publisher_get_current_duration)                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_data_publisher_get_total_duration,               \
                             zego_express_media_data_publisher_get_total_duration)                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_data_publisher_reset,                            \
                             zego_express_media_data_publisher_reset)                               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_data_publisher_seek_to,                          \
                             zego_express_media_data_publisher_seek_to)                             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_data_publisher_set_video_send_delay_time,        \
                             zego_express_media_data_publisher_set_video_send_delay_time)           \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_clear_view,                               \
                             zego_express_media_player_clear_view)                                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_enable_accurate_seek,                     \
                             zego_express_media_player_enable_accurate_seek)                        \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_enable_audio_data,                        \
                             zego_express_media_player_enable_audio_data)                           \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_enable_aux,                               \
                             zego_express_media_player_enable_aux)                                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_enable_block_data,                        \
                             zego_express_media_player_enable_block_data)                           \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_enable_frequency_spectrum_monitor,        \
                             zego_express_media_player_enable_frequency_spectrum_monitor)           \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_enable_live_audio_effect,                 \
                             zego_express_media_player_enable_live_audio_effect)                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_enable_local_cache,                       \
                             zego_express_media_player_enable_local_cache)                          \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_enable_repeat,                            \
                             zego_express_media_player_enable_repeat)                               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_enable_sound_level_monitor,               \
                             zego_express_media_player_enable_sound_level_monitor)                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_enable_video_data,                        \
                             zego_express_media_player_enable_video_data)                           \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_enable_view_mirror,                       \
                             zego_express_media_player_enable_view_mirror)                          \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_enable_voice_changer,                     \
                             zego_express_media_player_enable_voice_changer)                        \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_get_audio_track_count,                    \
                             zego_express_media_player_get_audio_track_count)                       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_get_current_progress,                     \
                             zego_express_media_player_get_current_progress)                        \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_get_current_rendering_progress,           \
                             zego_express_media_player_get_current_rendering_progress)              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_get_current_state,                        \
                             zego_express_media_player_get_current_state)                           \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_get_media_info,                           \
                             zego_express_media_player_get_media_info)                              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_get_network_resource_cache,               \
                             zego_express_media_player_get_network_resource_cache)                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_get_play_volume,                          \
                             zego_express_media_player_get_play_volume)                             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_get_playback_statistics,                  \
                             zego_express_media_player_get_playback_statistics)                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_get_publish_volume,                       \
                             zego_express_media_player_get_publish_volume)                          \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_get_total_duration,                       \
                             zego_express_media_player_get_total_duration)                          \
    ZEGOEXP_DECLARE_FUNC_PTR(                                                                       \
        pfnzego_express_media_player_load_copyrighted_music_resource_with_position,                 \
        zego_express_media_player_load_copyrighted_music_resource_with_position)                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_load_resource,                            \
                             zego_express_media_player_load_resource)                               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_load_resource_from_media_data,            \
                             zego_express_media_player_load_resource_from_media_data)               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_load_resource_with_config,                \
                             zego_express_media_player_load_resource_with_config)                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_load_resource_with_position,              \
                             zego_express_media_player_load_resource_with_position)                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_mute_local_audio,                         \
                             zego_express_media_player_mute_local_audio)                            \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_pause, zego_express_media_player_pause)   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_resume,                                   \
                             zego_express_media_player_resume)                                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_seek_to,                                  \
                             zego_express_media_player_seek_to)                                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_set_active_audio_channel,                 \
                             zego_express_media_player_set_active_audio_channel)                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_set_audio_track_index,                    \
                             zego_express_media_player_set_audio_track_index)                       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_set_audio_track_mode,                     \
                             zego_express_media_player_set_audio_track_mode)                        \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_set_audio_track_publish_index,            \
                             zego_express_media_player_set_audio_track_publish_index)               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_set_http_header,                          \
                             zego_express_media_player_set_http_header)                             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_set_network_buffer_threshold,             \
                             zego_express_media_player_set_network_buffer_threshold)                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_set_network_resource_max_cache,           \
                             zego_express_media_player_set_network_resource_max_cache)              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_set_play_loop_count,                      \
                             zego_express_media_player_set_play_loop_count)                         \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_set_play_media_stream_type,               \
                             zego_express_media_player_set_play_media_stream_type)                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_set_play_speed,                           \
                             zego_express_media_player_set_play_speed)                              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_set_play_volume,                          \
                             zego_express_media_player_set_play_volume)                             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_set_player_canvas,                        \
                             zego_express_media_player_set_player_canvas)                           \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_set_progress_interval,                    \
                             zego_express_media_player_set_progress_interval)                       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_set_publish_volume,                       \
                             zego_express_media_player_set_publish_volume)                          \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_set_voice_changer_param,                  \
                             zego_express_media_player_set_voice_changer_param)                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_set_volume,                               \
                             zego_express_media_player_set_volume)                                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_start, zego_express_media_player_start)   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_stop, zego_express_media_player_stop)     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_take_snapshot,                            \
                             zego_express_media_player_take_snapshot)                               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_media_player_update_position,                          \
                             zego_express_media_player_update_position)                             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_mute_all_play_audio_streams,                           \
                             zego_express_mute_all_play_audio_streams)                              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_mute_all_play_stream_audio,                            \
                             zego_express_mute_all_play_stream_audio)                               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_mute_all_play_stream_video,                            \
                             zego_express_mute_all_play_stream_video)                               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_mute_all_play_video_streams,                           \
                             zego_express_mute_all_play_video_streams)                              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_mute_audio_device, zego_express_mute_audio_device)     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_mute_local_audio_mixing,                               \
                             zego_express_mute_local_audio_mixing)                                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_mute_microphone, zego_express_mute_microphone)         \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_mute_play_stream_audio,                                \
                             zego_express_mute_play_stream_audio)                                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_mute_play_stream_video,                                \
                             zego_express_mute_play_stream_video)                                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_mute_publish_stream_audio,                             \
                             zego_express_mute_publish_stream_audio)                                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_mute_publish_stream_video,                             \
                             zego_express_mute_publish_stream_video)                                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_mute_speaker, zego_express_mute_speaker)               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_post_windows_message,                                  \
                             zego_express_post_windows_message)                                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_range_audio_enable_microphone,                         \
                             zego_express_range_audio_enable_microphone)                            \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_range_audio_enable_spatializer,                        \
                             zego_express_range_audio_enable_spatializer)                           \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_range_audio_enable_speaker,                            \
                             zego_express_range_audio_enable_speaker)                               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_range_audio_mute_user,                                 \
                             zego_express_range_audio_mute_user)                                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_range_audio_set_audio_receive_range,                   \
                             zego_express_range_audio_set_audio_receive_range)                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_range_audio_set_audio_receive_range_with_param,        \
                             zego_express_range_audio_set_audio_receive_range_with_param)           \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_range_audio_set_audio_volume,                          \
                             zego_express_range_audio_set_audio_volume)                             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_range_audio_set_position_update_frequency,             \
                             zego_express_range_audio_set_position_update_frequency)                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_range_audio_set_stream_vocal_range,                    \
                             zego_express_range_audio_set_stream_vocal_range)                       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_range_audio_set_stream_vocal_range_with_param,         \
                             zego_express_range_audio_set_stream_vocal_range_with_param)            \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_range_audio_set_team_id,                               \
                             zego_express_range_audio_set_team_id)                                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_range_audio_update_audio_source,                       \
                             zego_express_range_audio_update_audio_source)                          \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_range_audio_update_self_position,                      \
                             zego_express_range_audio_update_self_position)                         \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_range_audio_update_stream_position,                    \
                             zego_express_range_audio_update_stream_position)                       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_range_scene_get_user_count,                            \
                             zego_express_range_scene_get_user_count)                               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_range_scene_get_user_list_in_view,                     \
                             zego_express_range_scene_get_user_list_in_view)                        \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_range_scene_item_bind_item,                            \
                             zego_express_range_scene_item_bind_item)                               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_range_scene_item_create_item,                          \
                             zego_express_range_scene_item_create_item)                             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_range_scene_item_destroy_item,                         \
                             zego_express_range_scene_item_destroy_item)                            \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_range_scene_item_unbind_item,                          \
                             zego_express_range_scene_item_unbind_item)                             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_range_scene_item_update_item_command,                  \
                             zego_express_range_scene_item_update_item_command)                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_range_scene_item_update_item_status,                   \
                             zego_express_range_scene_item_update_item_status)                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_range_scene_login_scene,                               \
                             zego_express_range_scene_login_scene)                                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_range_scene_logout_scene,                              \
                             zego_express_range_scene_logout_scene)                                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_range_scene_renew_token,                               \
                             zego_express_range_scene_renew_token)                                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_range_scene_send_custom_command,                       \
                             zego_express_range_scene_send_custom_command)                          \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_range_scene_set_stream_config,                         \
                             zego_express_range_scene_set_stream_config)                            \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_range_scene_stream_enable_range_spatializer,           \
                             zego_express_range_scene_stream_enable_range_spatializer)              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_range_scene_stream_mute_play_audio,                    \
                             zego_express_range_scene_stream_mute_play_audio)                       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_range_scene_stream_mute_play_video,                    \
                             zego_express_range_scene_stream_mute_play_video)                       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_range_scene_stream_set_receive_range,                  \
                             zego_express_range_scene_stream_set_receive_range)                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_range_scene_stream_set_receive_range_with_param,       \
                             zego_express_range_scene_stream_set_receive_range_with_param)          \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_range_scene_team_join_team,                            \
                             zego_express_range_scene_team_join_team)                               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_range_scene_team_leave_team,                           \
                             zego_express_range_scene_team_leave_team)                              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_range_scene_update_user_command,                       \
                             zego_express_range_scene_update_user_command)                          \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_range_scene_update_user_position,                      \
                             zego_express_range_scene_update_user_position)                         \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_range_scene_update_user_status,                        \
                             zego_express_range_scene_update_user_status)                           \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_real_time_sequential_data_start_broadcasting,          \
                             zego_express_real_time_sequential_data_start_broadcasting)             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_real_time_sequential_data_start_subscribing,           \
                             zego_express_real_time_sequential_data_start_subscribing)              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_real_time_sequential_data_stop_broadcasting,           \
                             zego_express_real_time_sequential_data_stop_broadcasting)              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_real_time_sequential_data_stop_subscribing,            \
                             zego_express_real_time_sequential_data_stop_subscribing)               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_release_unity_captured_texture,                        \
                             zego_express_release_unity_captured_texture)                           \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_release_unity_played_texture,                          \
                             zego_express_release_unity_played_texture)                             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_remove_dump_data, zego_express_remove_dump_data)       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_remove_publish_cdn_url,                                \
                             zego_express_remove_publish_cdn_url)                                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_renew_token, zego_express_renew_token)                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_reset_custom_video_capture_texture_context,            \
                             zego_express_reset_custom_video_capture_texture_context)               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_screen_capture_enable_audio_capture,                   \
                             zego_express_screen_capture_enable_audio_capture)                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_screen_capture_enable_cursor_visible,                  \
                             zego_express_screen_capture_enable_cursor_visible)                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_screen_capture_enable_window_activate,                 \
                             zego_express_screen_capture_enable_window_activate)                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_screen_capture_get_capture_source_rect,                \
                             zego_express_screen_capture_get_capture_source_rect)                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_screen_capture_set_app_group_id_ios,                   \
                             zego_express_screen_capture_set_app_group_id_ios)                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_screen_capture_set_exclude_window_list,                \
                             zego_express_screen_capture_set_exclude_window_list)                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_screen_capture_start_capture,                          \
                             zego_express_screen_capture_start_capture)                             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_screen_capture_stop_capture,                           \
                             zego_express_screen_capture_stop_capture)                              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_screen_capture_stop_capture_mobile,                    \
                             zego_express_screen_capture_stop_capture_mobile)                       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_screen_capture_update_capture_region,                  \
                             zego_express_screen_capture_update_capture_region)                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_screen_capture_update_capture_source,                  \
                             zego_express_screen_capture_update_capture_source)                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_screen_capture_update_publish_region,                  \
                             zego_express_screen_capture_update_publish_region)                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_send_audio_side_info,                                  \
                             zego_express_send_audio_side_info)                                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_send_barrage_message,                                  \
                             zego_express_send_barrage_message)                                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_send_broadcast_message,                                \
                             zego_express_send_broadcast_message)                                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_send_custom_audio_capture_aac_data,                    \
                             zego_express_send_custom_audio_capture_aac_data)                       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_send_custom_audio_capture_pcm_data,                    \
                             zego_express_send_custom_audio_capture_pcm_data)                       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_send_custom_command,                                   \
                             zego_express_send_custom_command)                                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_send_custom_video_capture_d3d_texture_data,            \
                             zego_express_send_custom_video_capture_d3d_texture_data)               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_send_custom_video_capture_encoded_data,                \
                             zego_express_send_custom_video_capture_encoded_data)                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_send_custom_video_capture_pixel_buffer,                \
                             zego_express_send_custom_video_capture_pixel_buffer)                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_send_custom_video_capture_raw_data,                    \
                             zego_express_send_custom_video_capture_raw_data)                       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_send_custom_video_capture_texture_data,                \
                             zego_express_send_custom_video_capture_texture_data)                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_send_custom_video_processed_cv_pixel_buffer,           \
                             zego_express_send_custom_video_processed_cv_pixel_buffer)              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_send_custom_video_processed_cv_pixel_buffer_v2,        \
                             zego_express_send_custom_video_processed_cv_pixel_buffer_v2)           \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_send_custom_video_processed_raw_data,                  \
                             zego_express_send_custom_video_processed_raw_data)                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_send_custom_video_processed_raw_data_v2,               \
                             zego_express_send_custom_video_processed_raw_data_v2)                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_send_custom_video_processed_texture_data,              \
                             zego_express_send_custom_video_processed_texture_data)                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_send_custom_video_processed_texture_data_v2,           \
                             zego_express_send_custom_video_processed_texture_data_v2)              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_send_real_time_sequential_data,                        \
                             zego_express_send_real_time_sequential_data)                           \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_send_reference_audio_pcm_data,                         \
                             zego_express_send_reference_audio_pcm_data)                            \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_send_sei, zego_express_send_sei)                       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_send_sei_sync_with_custom_video,                       \
                             zego_express_send_sei_sync_with_custom_video)                          \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_send_transparent_message,                              \
                             zego_express_send_transparent_message)                                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_aec_mode, zego_express_set_aec_mode)               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_all_play_stream_volume,                            \
                             zego_express_set_all_play_stream_volume)                               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_android_env, zego_express_set_android_env)         \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_ans_mode, zego_express_set_ans_mode)               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_app_orientation,                                   \
                             zego_express_set_app_orientation)                                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_app_orientation_mode,                              \
                             zego_express_set_app_orientation_mode)                                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_audio_capture_stereo_mode,                         \
                             zego_express_set_audio_capture_stereo_mode)                            \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_audio_config, zego_express_set_audio_config)       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_audio_config_by_channel,                           \
                             zego_express_set_audio_config_by_channel)                              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_audio_device_mode,                                 \
                             zego_express_set_audio_device_mode)                                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_audio_device_volume,                               \
                             zego_express_set_audio_device_volume)                                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_audio_equalizer_gain,                              \
                             zego_express_set_audio_equalizer_gain)                                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_audio_mixing_volume,                               \
                             zego_express_set_audio_mixing_volume)                                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_audio_mixing_volume_with_type,                     \
                             zego_express_set_audio_mixing_volume_with_type)                        \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_audio_route_to_speaker,                            \
                             zego_express_set_audio_route_to_speaker)                               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_audio_source, zego_express_set_audio_source)       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_audio_source_with_config,                          \
                             zego_express_set_audio_source_with_config)                             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_beautify_option,                                   \
                             zego_express_set_beautify_option)                                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_camera_exposure_compensation,                      \
                             zego_express_set_camera_exposure_compensation)                         \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_camera_exposure_mode,                              \
                             zego_express_set_camera_exposure_mode)                                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_camera_exposure_point_in_preview,                  \
                             zego_express_set_camera_exposure_point_in_preview)                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_camera_focus_mode,                                 \
                             zego_express_set_camera_focus_mode)                                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_camera_focus_point_in_preview,                     \
                             zego_express_set_camera_focus_point_in_preview)                        \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_camera_stabilization_mode,                         \
                             zego_express_set_camera_stabilization_mode)                            \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_camera_zoom_factor,                                \
                             zego_express_set_camera_zoom_factor)                                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_capture_pipeline_scale_mode,                       \
                             zego_express_set_capture_pipeline_scale_mode)                          \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_capture_volume, zego_express_set_capture_volume)   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_cloud_proxy_config,                                \
                             zego_express_set_cloud_proxy_config)                                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_custom_video_capture_device_state,                 \
                             zego_express_set_custom_video_capture_device_state)                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_custom_video_capture_fill_mode,                    \
                             zego_express_set_custom_video_capture_fill_mode)                       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_custom_video_capture_flip_mode,                    \
                             zego_express_set_custom_video_capture_flip_mode)                       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_custom_video_capture_region_of_interest,           \
                             zego_express_set_custom_video_capture_region_of_interest)              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_custom_video_capture_rotation,                     \
                             zego_express_set_custom_video_capture_rotation)                        \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_custom_video_capture_transform_matrix,             \
                             zego_express_set_custom_video_capture_transform_matrix)                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_dummy_capture_image_path,                          \
                             zego_express_set_dummy_capture_image_path)                             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_effects_beauty_param,                              \
                             zego_express_set_effects_beauty_param)                                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_electronic_effects,                                \
                             zego_express_set_electronic_effects)                                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_engine_config, zego_express_set_engine_config)     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_geo_fence, zego_express_set_geo_fence)             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_headphone_monitor_volume,                          \
                             zego_express_set_headphone_monitor_volume)                             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_ios_app_orientation,                               \
                             zego_express_set_ios_app_orientation)                                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_license, zego_express_set_license)                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_local_proxy_config,                                \
                             zego_express_set_local_proxy_config)                                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_log_config, zego_express_set_log_config)           \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_low_light_enhancement,                             \
                             zego_express_set_low_light_enhancement)                                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_min_video_bitrate_for_traffic_control,             \
                             zego_express_set_min_video_bitrate_for_traffic_control)                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_min_video_bitrate_for_traffic_control_by_channel,  \
                             zego_express_set_min_video_bitrate_for_traffic_control_by_channel)     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_min_video_fps_for_traffic_control_by_channel,      \
                             zego_express_set_min_video_fps_for_traffic_control_by_channel)         \
    ZEGOEXP_DECLARE_FUNC_PTR(                                                                       \
        pfnzego_express_set_min_video_resolution_for_traffic_control_by_channel,                    \
        zego_express_set_min_video_resolution_for_traffic_control_by_channel)                       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_mix_system_playout_volume,                         \
                             zego_express_set_mix_system_playout_volume)                            \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_platform_language,                                 \
                             zego_express_set_platform_language)                                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_play_stream_buffer_interval_range,                 \
                             zego_express_set_play_stream_buffer_interval_range)                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_play_stream_cross_app_info,                        \
                             zego_express_set_play_stream_cross_app_info)                           \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_play_stream_decryption_key,                        \
                             zego_express_set_play_stream_decryption_key)                           \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_play_stream_focus_on,                              \
                             zego_express_set_play_stream_focus_on)                                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_play_stream_video_type,                            \
                             zego_express_set_play_stream_video_type)                               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_play_streams_alignment_property,                   \
                             zego_express_set_play_streams_alignment_property)                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_play_volume, zego_express_set_play_volume)         \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_publish_dual_stream_config,                        \
                             zego_express_set_publish_dual_stream_config)                           \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_publish_stream_encryption_key,                     \
                             zego_express_set_publish_stream_encryption_key)                        \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_publish_watermark,                                 \
                             zego_express_set_publish_watermark)                                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_range_audio_custom_mode,                           \
                             zego_express_set_range_audio_custom_mode)                              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_range_audio_mode,                                  \
                             zego_express_set_range_audio_mode)                                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_reverb_advanced_param,                             \
                             zego_express_set_reverb_advanced_param)                                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_reverb_echo_param,                                 \
                             zego_express_set_reverb_echo_param)                                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_reverb_preset, zego_express_set_reverb_preset)     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_room_extra_info,                                   \
                             zego_express_set_room_extra_info)                                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_room_mode, zego_express_set_room_mode)             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_room_scenario, zego_express_set_room_scenario)     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_sei_config, zego_express_set_sei_config)           \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_speaker_volume_in_app,                             \
                             zego_express_set_speaker_volume_in_app)                                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_stream_alignment_property,                         \
                             zego_express_set_stream_alignment_property)                            \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_stream_extra_info,                                 \
                             zego_express_set_stream_extra_info)                                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_traffic_control_focus_on,                          \
                             zego_express_set_traffic_control_focus_on)                             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_traffic_control_focus_on_by_channel,               \
                             zego_express_set_traffic_control_focus_on_by_channel)                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_video_config, zego_express_set_video_config)       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_video_mirror_mode,                                 \
                             zego_express_set_video_mirror_mode)                                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_video_source, zego_express_set_video_source)       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_voice_changer_param,                               \
                             zego_express_set_voice_changer_param)                                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_set_voice_changer_preset,                              \
                             zego_express_set_voice_changer_preset)                                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_start_audio_data_observer,                             \
                             zego_express_start_audio_data_observer)                                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_start_audio_device_volume_monitor,                     \
                             zego_express_start_audio_device_volume_monitor)                        \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_start_audio_spectrum_monitor,                          \
                             zego_express_start_audio_spectrum_monitor)                             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_start_audio_vad_stable_state_monitor,                  \
                             zego_express_start_audio_vad_stable_state_monitor)                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_start_auto_mixer_task,                                 \
                             zego_express_start_auto_mixer_task)                                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_start_dump_data, zego_express_start_dump_data)         \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_start_effects_env, zego_express_start_effects_env)     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_start_mixer_task, zego_express_start_mixer_task)       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_start_network_probe,                                   \
                             zego_express_start_network_probe)                                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_start_network_speed_test,                              \
                             zego_express_start_network_speed_test)                                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_start_performance_monitor,                             \
                             zego_express_start_performance_monitor)                                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_start_playing_stream,                                  \
                             zego_express_start_playing_stream)                                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_start_playing_stream_in_scene,                         \
                             zego_express_start_playing_stream_in_scene)                            \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_start_playing_stream_with_config,                      \
                             zego_express_start_playing_stream_with_config)                         \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_start_preview, zego_express_start_preview)             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_start_publishing_stream,                               \
                             zego_express_start_publishing_stream)                                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_start_publishing_stream_in_scene,                      \
                             zego_express_start_publishing_stream_in_scene)                         \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_start_publishing_stream_with_config,                   \
                             zego_express_start_publishing_stream_with_config)                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_start_recording_captured_data,                         \
                             zego_express_start_recording_captured_data)                            \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_start_screen_capture_in_app_ios,                       \
                             zego_express_start_screen_capture_in_app_ios)                          \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_start_screen_capture_mobile,                           \
                             zego_express_start_screen_capture_mobile)                              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_start_sound_level_monitor,                             \
                             zego_express_start_sound_level_monitor)                                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_start_sound_level_monitor_with_config,                 \
                             zego_express_start_sound_level_monitor_with_config)                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_stop_audio_data_observer,                              \
                             zego_express_stop_audio_data_observer)                                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_stop_audio_device_volume_monitor,                      \
                             zego_express_stop_audio_device_volume_monitor)                         \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_stop_audio_spectrum_monitor,                           \
                             zego_express_stop_audio_spectrum_monitor)                              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_stop_audio_vad_stable_state_monitor,                   \
                             zego_express_stop_audio_vad_stable_state_monitor)                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_stop_auto_mixer_task,                                  \
                             zego_express_stop_auto_mixer_task)                                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_stop_dump_data, zego_express_stop_dump_data)           \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_stop_effects_env, zego_express_stop_effects_env)       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_stop_mixer_task, zego_express_stop_mixer_task)         \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_stop_network_probe, zego_express_stop_network_probe)   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_stop_network_speed_test,                               \
                             zego_express_stop_network_speed_test)                                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_stop_performance_monitor,                              \
                             zego_express_stop_performance_monitor)                                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_stop_playing_stream,                                   \
                             zego_express_stop_playing_stream)                                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_stop_preview, zego_express_stop_preview)               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_stop_publishing_stream,                                \
                             zego_express_stop_publishing_stream)                                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_stop_recording_captured_data,                          \
                             zego_express_stop_recording_captured_data)                             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_stop_sound_level_monitor,                              \
                             zego_express_stop_sound_level_monitor)                                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_switch_playing_stream,                                 \
                             zego_express_switch_playing_stream)                                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_switch_room, zego_express_switch_room)                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_take_play_stream_snapshot,                             \
                             zego_express_take_play_stream_snapshot)                                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_take_publish_stream_snapshot,                          \
                             zego_express_take_publish_stream_snapshot)                             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_test_network_connectivity,                             \
                             zego_express_test_network_connectivity)                                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_uninit_video_super_resolution,                         \
                             zego_express_uninit_video_super_resolution)                            \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_update_playing_canvas,                                 \
                             zego_express_update_playing_canvas)                                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_update_screen_capture_config_mobile,                   \
                             zego_express_update_screen_capture_config_mobile)                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_upload_dump_data, zego_express_upload_dump_data)       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_upload_log, zego_express_upload_log)                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_use_audio_device, zego_express_use_audio_device)       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_use_front_camera, zego_express_use_front_camera)       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_express_use_video_device, zego_express_use_video_device)       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_ai_voice_changer_event_callback,                      \
                             zego_register_ai_voice_changer_event_callback)                         \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_ai_voice_changer_get_speaker_list_callback,           \
                             zego_register_ai_voice_changer_get_speaker_list_callback)              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_ai_voice_changer_init_callback,                       \
                             zego_register_ai_voice_changer_init_callback)                          \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_ai_voice_changer_set_speaker_callback,                \
                             zego_register_ai_voice_changer_set_speaker_callback)                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_ai_voice_changer_update_callback,                     \
                             zego_register_ai_voice_changer_update_callback)                        \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_ai_voice_changer_update_progress_callback,            \
                             zego_register_ai_voice_changer_update_progress_callback)               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_aligned_audio_aux_data_callback,                      \
                             zego_register_aligned_audio_aux_data_callback)                         \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_api_called_result_callback,                           \
                             zego_register_api_called_result_callback)                              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_audio_device_state_changed_callback,                  \
                             zego_register_audio_device_state_changed_callback)                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_audio_device_volume_changed_callback,                 \
                             zego_register_audio_device_volume_changed_callback)                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_audio_effect_play_state_update_callback,              \
                             zego_register_audio_effect_play_state_update_callback)                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_audio_effect_player_load_resource_callback,           \
                             zego_register_audio_effect_player_load_resource_callback)              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_audio_effect_player_seek_to_callback,                 \
                             zego_register_audio_effect_player_seek_to_callback)                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_audio_route_change_callback,                          \
                             zego_register_audio_route_change_callback)                             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_audio_vad_state_update_callback,                      \
                             zego_register_audio_vad_state_update_callback)                         \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_auto_mixer_sound_level_update_callback,               \
                             zego_register_auto_mixer_sound_level_update_callback)                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_auto_mixer_start_result_callback,                     \
                             zego_register_auto_mixer_start_result_callback)                        \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_auto_mixer_stop_result_callback,                      \
                             zego_register_auto_mixer_stop_result_callback)                         \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_before_audio_prep_audio_data_callback,                \
                             zego_register_before_audio_prep_audio_data_callback)                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_captured_audio_data_callback,                         \
                             zego_register_captured_audio_data_callback)                            \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_captured_audio_spectrum_update_callback,              \
                             zego_register_captured_audio_spectrum_update_callback)                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_captured_data_record_progress_update_callback,        \
                             zego_register_captured_data_record_progress_update_callback)           \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_captured_data_record_state_update_callback,           \
                             zego_register_captured_data_record_state_update_callback)              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_captured_sound_level_info_update_callback,            \
                             zego_register_captured_sound_level_info_update_callback)               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_captured_sound_level_update_callback,                 \
                             zego_register_captured_sound_level_update_callback)                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_copy_audio_mixing_data_callback,                      \
                             zego_register_copy_audio_mixing_data_callback)                         \
    ZEGOEXP_DECLARE_FUNC_PTR(                                                                       \
        pfnzego_register_copyrighted_music_current_pitch_value_update_callback,                     \
        zego_register_copyrighted_music_current_pitch_value_update_callback)                        \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_copyrighted_music_download_callback,                  \
                             zego_register_copyrighted_music_download_callback)                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_copyrighted_music_download_progress_update_callback,  \
                             zego_register_copyrighted_music_download_progress_update_callback)     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_copyrighted_music_get_krc_lyric_by_token_callback,    \
                             zego_register_copyrighted_music_get_krc_lyric_by_token_callback)       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_copyrighted_music_get_lrc_lyric_callback,             \
                             zego_register_copyrighted_music_get_lrc_lyric_callback)                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_copyrighted_music_get_music_by_token_callback,        \
                             zego_register_copyrighted_music_get_music_by_token_callback)           \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_copyrighted_music_get_shared_resource_callback,       \
                             zego_register_copyrighted_music_get_shared_resource_callback)          \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_copyrighted_music_get_standard_pitch_callback,        \
                             zego_register_copyrighted_music_get_standard_pitch_callback)           \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_copyrighted_music_init_callback,                      \
                             zego_register_copyrighted_music_init_callback)                         \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_copyrighted_music_request_accompaniment_callback,     \
                             zego_register_copyrighted_music_request_accompaniment_callback)        \
    ZEGOEXP_DECLARE_FUNC_PTR(                                                                       \
        pfnzego_register_copyrighted_music_request_accompaniment_clip_callback,                     \
        zego_register_copyrighted_music_request_accompaniment_clip_callback)                        \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_copyrighted_music_request_resource_callback,          \
                             zego_register_copyrighted_music_request_resource_callback)             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_copyrighted_music_request_song_callback,              \
                             zego_register_copyrighted_music_request_song_callback)                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_copyrighted_music_send_extended_request_callback,     \
                             zego_register_copyrighted_music_send_extended_request_callback)        \
    ZEGOEXP_DECLARE_FUNC_PTR(                                                                       \
        pfnzego_register_custom_video_capture_encoded_data_traffic_control_callback,                \
        zego_register_custom_video_capture_encoded_data_traffic_control_callback)                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_custom_video_capture_frame_rate_callback,             \
                             zego_register_custom_video_capture_frame_rate_callback)                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_custom_video_capture_start_callback,                  \
                             zego_register_custom_video_capture_start_callback)                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_custom_video_capture_stop_callback,                   \
                             zego_register_custom_video_capture_stop_callback)                      \
    ZEGOEXP_DECLARE_FUNC_PTR(                                                                       \
        pfnzego_register_custom_video_process_captured_unprocessed_cvpixelbuffer_callback,          \
        zego_register_custom_video_process_captured_unprocessed_cvpixelbuffer_callback)             \
    ZEGOEXP_DECLARE_FUNC_PTR(                                                                       \
        pfnzego_register_custom_video_process_captured_unprocessed_raw_data_callback,               \
        zego_register_custom_video_process_captured_unprocessed_raw_data_callback)                  \
    ZEGOEXP_DECLARE_FUNC_PTR(                                                                       \
        pfnzego_register_custom_video_process_captured_unprocessed_texture_data_callback,           \
        zego_register_custom_video_process_captured_unprocessed_texture_data_callback)              \
    ZEGOEXP_DECLARE_FUNC_PTR(                                                                       \
        pfnzego_register_custom_video_process_get_input_surface_texture_callback,                   \
        zego_register_custom_video_process_get_input_surface_texture_callback)                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_custom_video_process_start_callback,                  \
                             zego_register_custom_video_process_start_callback)                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_custom_video_process_stop_callback,                   \
                             zego_register_custom_video_process_stop_callback)                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_custom_video_render_captured_frame_data_callback,     \
                             zego_register_custom_video_render_captured_frame_data_callback)        \
    ZEGOEXP_DECLARE_FUNC_PTR(                                                                       \
        pfnzego_register_custom_video_render_captured_frame_pixel_buffer_callback,                  \
        zego_register_custom_video_render_captured_frame_pixel_buffer_callback)                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_custom_video_render_remote_frame_data_callback,       \
                             zego_register_custom_video_render_remote_frame_data_callback)          \
    ZEGOEXP_DECLARE_FUNC_PTR(                                                                       \
        pfnzego_register_custom_video_render_remote_frame_encoded_data_callback,                    \
        zego_register_custom_video_render_remote_frame_encoded_data_callback)                       \
    ZEGOEXP_DECLARE_FUNC_PTR(                                                                       \
        pfnzego_register_custom_video_render_remote_frame_pixel_buffer_callback,                    \
        zego_register_custom_video_render_remote_frame_pixel_buffer_callback)                       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_debug_error_callback,                                 \
                             zego_register_debug_error_callback)                                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_engine_state_update_callback,                         \
                             zego_register_engine_state_update_callback)                            \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_engine_uninit_callback,                               \
                             zego_register_engine_uninit_callback)                                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_fatal_error_callback,                                 \
                             zego_register_fatal_error_callback)                                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_im_recv_barrage_message_callback,                     \
                             zego_register_im_recv_barrage_message_callback)                        \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_im_recv_broadcast_message_callback,                   \
                             zego_register_im_recv_broadcast_message_callback)                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_im_recv_custom_command_callback,                      \
                             zego_register_im_recv_custom_command_callback)                         \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_im_send_barrage_message_result_callback,              \
                             zego_register_im_send_barrage_message_result_callback)                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_im_send_broadcast_message_result_callback,            \
                             zego_register_im_send_broadcast_message_result_callback)               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_im_send_custom_command_result_callback,               \
                             zego_register_im_send_custom_command_result_callback)                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_local_device_exception_occurred_callback,             \
                             zego_register_local_device_exception_occurred_callback)                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_media_data_publisher_file_close_callback,             \
                             zego_register_media_data_publisher_file_close_callback)                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_media_data_publisher_file_data_begin_callback,        \
                             zego_register_media_data_publisher_file_data_begin_callback)           \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_media_data_publisher_file_data_end_callback,          \
                             zego_register_media_data_publisher_file_data_end_callback)             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_media_data_publisher_file_open_callback,              \
                             zego_register_media_data_publisher_file_open_callback)                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_media_player_audio_frame_callback,                    \
                             zego_register_media_player_audio_frame_callback)                       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_media_player_block_begin_callback,                    \
                             zego_register_media_player_block_begin_callback)                       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_media_player_block_data_callback,                     \
                             zego_register_media_player_block_data_callback)                        \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_media_player_first_frame_event_callback,              \
                             zego_register_media_player_first_frame_event_callback)                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_media_player_frequency_spectrum_update_callback,      \
                             zego_register_media_player_frequency_spectrum_update_callback)         \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_media_player_load_resource_callback,                  \
                             zego_register_media_player_load_resource_callback)                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_media_player_local_cache_callback,                    \
                             zego_register_media_player_local_cache_callback)                       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_media_player_network_event_callback,                  \
                             zego_register_media_player_network_event_callback)                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_media_player_playing_progress_callback,               \
                             zego_register_media_player_playing_progress_callback)                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_media_player_recv_sei_callback,                       \
                             zego_register_media_player_recv_sei_callback)                          \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_media_player_rendering_progress_callback,             \
                             zego_register_media_player_rendering_progress_callback)                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_media_player_seek_to_callback,                        \
                             zego_register_media_player_seek_to_callback)                           \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_media_player_sound_level_update_callback,             \
                             zego_register_media_player_sound_level_update_callback)                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_media_player_state_update_callback,                   \
                             zego_register_media_player_state_update_callback)                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_media_player_take_snapshot_result_callback,           \
                             zego_register_media_player_take_snapshot_result_callback)              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_media_player_video_frame_callback,                    \
                             zego_register_media_player_video_frame_callback)                       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_media_player_video_size_changed_callback,             \
                             zego_register_media_player_video_size_changed_callback)                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_mixed_audio_data_callback,                            \
                             zego_register_mixed_audio_data_callback)                               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_mixer_relay_cdn_state_update_callback,                \
                             zego_register_mixer_relay_cdn_state_update_callback)                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_mixer_sound_level_update_callback,                    \
                             zego_register_mixer_sound_level_update_callback)                       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_mixer_start_result_callback,                          \
                             zego_register_mixer_start_result_callback)                             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_mixer_stop_result_callback,                           \
                             zego_register_mixer_stop_result_callback)                              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_network_mode_changed_callback,                        \
                             zego_register_network_mode_changed_callback)                           \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_network_probe_result_callback,                        \
                             zego_register_network_probe_result_callback)                           \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_network_quality_callback,                             \
                             zego_register_network_quality_callback)                                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_network_speed_test_error_callback,                    \
                             zego_register_network_speed_test_error_callback)                       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_network_speed_test_quality_update_callback,           \
                             zego_register_network_speed_test_quality_update_callback)              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_network_time_synchronized_callback,                   \
                             zego_register_network_time_synchronized_callback)                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_performance_status_update_callback,                   \
                             zego_register_performance_status_update_callback)                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_playback_audio_data_callback,                         \
                             zego_register_playback_audio_data_callback)                            \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_player_audio_data_callback,                           \
                             zego_register_player_audio_data_callback)                              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_player_low_fps_warning_callback,                      \
                             zego_register_player_low_fps_warning_callback)                         \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_player_media_event_callback,                          \
                             zego_register_player_media_event_callback)                             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_player_quality_update_callback,                       \
                             zego_register_player_quality_update_callback)                          \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_player_recv_audio_first_frame_callback,               \
                             zego_register_player_recv_audio_first_frame_callback)                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_player_recv_audio_side_info_callback,                 \
                             zego_register_player_recv_audio_side_info_callback)                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_player_recv_media_side_info_callback,                 \
                             zego_register_player_recv_media_side_info_callback)                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_player_recv_sei_callback,                             \
                             zego_register_player_recv_sei_callback)                                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_player_recv_video_first_frame_callback,               \
                             zego_register_player_recv_video_first_frame_callback)                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_player_render_camera_video_first_frame_callback,      \
                             zego_register_player_render_camera_video_first_frame_callback)         \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_player_render_video_first_frame_callback,             \
                             zego_register_player_render_video_first_frame_callback)                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_player_state_update_callback,                         \
                             zego_register_player_state_update_callback)                            \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_player_stream_event_callback,                         \
                             zego_register_player_stream_event_callback)                            \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_player_switched_callback,                             \
                             zego_register_player_switched_callback)                                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_player_take_snapshot_result_callback,                 \
                             zego_register_player_take_snapshot_result_callback)                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_player_video_size_changed_callback,                   \
                             zego_register_player_video_size_changed_callback)                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_player_video_super_resolution_update_callback,        \
                             zego_register_player_video_super_resolution_update_callback)           \
    ZEGOEXP_DECLARE_FUNC_PTR(                                                                       \
        pfnzego_register_process_captured_audio_data_after_used_headphone_monitor_callback,         \
        zego_register_process_captured_audio_data_after_used_headphone_monitor_callback)            \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_process_captured_audio_data_callback,                 \
                             zego_register_process_captured_audio_data_callback)                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_process_playback_audio_data_callback,                 \
                             zego_register_process_playback_audio_data_callback)                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_process_remote_audio_data_callback,                   \
                             zego_register_process_remote_audio_data_callback)                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_publisher_captured_audio_first_frame_callback,        \
                             zego_register_publisher_captured_audio_first_frame_callback)           \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_publisher_captured_video_first_frame_callback,        \
                             zego_register_publisher_captured_video_first_frame_callback)           \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_publisher_dummy_capture_image_path_error_callback,    \
                             zego_register_publisher_dummy_capture_image_path_error_callback)       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_publisher_low_fps_warning_callback,                   \
                             zego_register_publisher_low_fps_warning_callback)                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_publisher_quality_update_callback,                    \
                             zego_register_publisher_quality_update_callback)                       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_publisher_relay_cdn_state_update_callback,            \
                             zego_register_publisher_relay_cdn_state_update_callback)               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_publisher_render_video_first_frame_callback,          \
                             zego_register_publisher_render_video_first_frame_callback)             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_publisher_send_audio_first_frame_callback,            \
                             zego_register_publisher_send_audio_first_frame_callback)               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_publisher_send_video_first_frame_callback,            \
                             zego_register_publisher_send_video_first_frame_callback)               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_publisher_state_update_callback,                      \
                             zego_register_publisher_state_update_callback)                         \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_publisher_stream_event_callback,                      \
                             zego_register_publisher_stream_event_callback)                         \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_publisher_take_snapshot_result_callback,              \
                             zego_register_publisher_take_snapshot_result_callback)                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_publisher_update_cdn_url_result_callback,             \
                             zego_register_publisher_update_cdn_url_result_callback)                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_publisher_update_stream_extra_info_result_callback,   \
                             zego_register_publisher_update_stream_extra_info_result_callback)      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_publisher_video_encoder_changed_callback,             \
                             zego_register_publisher_video_encoder_changed_callback)                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_publisher_video_size_changed_callback,                \
                             zego_register_publisher_video_size_changed_callback)                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_range_audio_microphone_state_update_callback,         \
                             zego_register_range_audio_microphone_state_update_callback)            \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_range_scene_custom_command_update_callback,           \
                             zego_register_range_scene_custom_command_update_callback)              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_range_scene_enter_view_callback,                      \
                             zego_register_range_scene_enter_view_callback)                         \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_range_scene_get_user_count_callback,                  \
                             zego_register_range_scene_get_user_count_callback)                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_range_scene_get_user_list_in_view_callback,           \
                             zego_register_range_scene_get_user_list_in_view_callback)              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_range_scene_item_bind_item_callback,                  \
                             zego_register_range_scene_item_bind_item_callback)                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_range_scene_item_create_item_callback,                \
                             zego_register_range_scene_item_create_item_callback)                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_range_scene_item_destroy_item_callback,               \
                             zego_register_range_scene_item_destroy_item_callback)                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_range_scene_item_item_bind_update_callback,           \
                             zego_register_range_scene_item_item_bind_update_callback)              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_range_scene_item_item_command_update_callback,        \
                             zego_register_range_scene_item_item_command_update_callback)           \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_range_scene_item_item_enter_view_callback,            \
                             zego_register_range_scene_item_item_enter_view_callback)               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_range_scene_item_item_leave_view_callback,            \
                             zego_register_range_scene_item_item_leave_view_callback)               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_range_scene_item_item_status_update_callback,         \
                             zego_register_range_scene_item_item_status_update_callback)            \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_range_scene_item_item_unbind_update_callback,         \
                             zego_register_range_scene_item_item_unbind_update_callback)            \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_range_scene_item_unbind_item_callback,                \
                             zego_register_range_scene_item_unbind_item_callback)                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_range_scene_item_update_item_command_callback,        \
                             zego_register_range_scene_item_update_item_command_callback)           \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_range_scene_item_update_item_status_callback,         \
                             zego_register_range_scene_item_update_item_status_callback)            \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_range_scene_leave_view_callback,                      \
                             zego_register_range_scene_leave_view_callback)                         \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_range_scene_login_scene_callback,                     \
                             zego_register_range_scene_login_scene_callback)                        \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_range_scene_logout_scene_callback,                    \
                             zego_register_range_scene_logout_scene_callback)                       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_range_scene_scene_state_update_callback,              \
                             zego_register_range_scene_scene_state_update_callback)                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_range_scene_send_custom_command_callback,             \
                             zego_register_range_scene_send_custom_command_callback)                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_range_scene_stream_user_camera_update_callback,       \
                             zego_register_range_scene_stream_user_camera_update_callback)          \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_range_scene_stream_user_mic_update_callback,          \
                             zego_register_range_scene_stream_user_mic_update_callback)             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_range_scene_stream_user_speaker_update_callback,      \
                             zego_register_range_scene_stream_user_speaker_update_callback)         \
    ZEGOEXP_DECLARE_FUNC_PTR(                                                                       \
        pfnzego_register_range_scene_stream_user_stream_state_update_callback,                      \
        zego_register_range_scene_stream_user_stream_state_update_callback)                         \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_range_scene_team_join_team_callback,                  \
                             zego_register_range_scene_team_join_team_callback)                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_range_scene_team_leave_team_callback,                 \
                             zego_register_range_scene_team_leave_team_callback)                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_range_scene_team_team_member_update_callback,         \
                             zego_register_range_scene_team_team_member_update_callback)            \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_range_scene_team_team_state_update_callback,          \
                             zego_register_range_scene_team_team_state_update_callback)             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_range_scene_token_will_expire_callback,               \
                             zego_register_range_scene_token_will_expire_callback)                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_range_scene_user_command_update_callback,             \
                             zego_register_range_scene_user_command_update_callback)                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_range_scene_user_status_update_callback,              \
                             zego_register_range_scene_user_status_update_callback)                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_real_time_sequential_data_sent_callback,              \
                             zego_register_real_time_sequential_data_sent_callback)                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_receive_real_time_sequential_data_callback,           \
                             zego_register_receive_real_time_sequential_data_callback)              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_recv_experimental_api_callback,                       \
                             zego_register_recv_experimental_api_callback)                          \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_recv_room_transparent_message_callback,               \
                             zego_register_recv_room_transparent_message_callback)                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_recv_windows_message_callback,                        \
                             zego_register_recv_windows_message_callback)                           \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_remote_audio_spectrum_update_callback,                \
                             zego_register_remote_audio_spectrum_update_callback)                   \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_remote_camera_state_update_callback,                  \
                             zego_register_remote_camera_state_update_callback)                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_remote_mic_state_update_callback,                     \
                             zego_register_remote_mic_state_update_callback)                        \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_remote_sound_level_info_update_callback,              \
                             zego_register_remote_sound_level_info_update_callback)                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_remote_sound_level_update_callback,                   \
                             zego_register_remote_sound_level_update_callback)                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_remote_speaker_state_update_callback,                 \
                             zego_register_remote_speaker_state_update_callback)                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_request_dump_data_callback,                           \
                             zego_register_request_dump_data_callback)                              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_request_upload_dump_data_callback,                    \
                             zego_register_request_upload_dump_data_callback)                       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_room_extra_info_update_callback,                      \
                             zego_register_room_extra_info_update_callback)                         \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_room_login_result_callback,                           \
                             zego_register_room_login_result_callback)                              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_room_logout_result_callback,                          \
                             zego_register_room_logout_result_callback)                             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_room_online_user_count_update_callback,               \
                             zego_register_room_online_user_count_update_callback)                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_room_set_room_extra_info_result_callback,             \
                             zego_register_room_set_room_extra_info_result_callback)                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_room_state_changed_callback,                          \
                             zego_register_room_state_changed_callback)                             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_room_state_update_callback,                           \
                             zego_register_room_state_update_callback)                              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_room_stream_extra_info_update_callback,               \
                             zego_register_room_stream_extra_info_update_callback)                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_room_stream_update_callback,                          \
                             zego_register_room_stream_update_callback)                             \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_room_token_will_expire_callback,                      \
                             zego_register_room_token_will_expire_callback)                         \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_room_user_update_callback,                            \
                             zego_register_room_user_update_callback)                               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_screen_capture_mobile_exception_occurred_callback,    \
                             zego_register_screen_capture_mobile_exception_occurred_callback)       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_screen_capture_mobile_start_callback,                 \
                             zego_register_screen_capture_mobile_start_callback)                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_screen_capture_rect_changed_callback,                 \
                             zego_register_screen_capture_rect_changed_callback)                    \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_screen_capture_source_available_frame_callback,       \
                             zego_register_screen_capture_source_available_frame_callback)          \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_screen_capture_source_exception_occurred_callback,    \
                             zego_register_screen_capture_source_exception_occurred_callback)       \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_screen_capture_window_state_changed_callback,         \
                             zego_register_screen_capture_window_state_changed_callback)            \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_send_transparent_message_result_callback,             \
                             zego_register_send_transparent_message_result_callback)                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_start_dump_data_callback,                             \
                             zego_register_start_dump_data_callback)                                \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_stop_dump_data_callback,                              \
                             zego_register_stop_dump_data_callback)                                 \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_test_network_connectivity_callback,                   \
                             zego_register_test_network_connectivity_callback)                      \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_unity_surface_texture_created_callback,               \
                             zego_register_unity_surface_texture_created_callback)                  \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_upload_dump_data_callback,                            \
                             zego_register_upload_dump_data_callback)                               \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_upload_log_result_callback,                           \
                             zego_register_upload_log_result_callback)                              \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_video_device_state_changed_callback,                  \
                             zego_register_video_device_state_changed_callback)                     \
    ZEGOEXP_DECLARE_FUNC_PTR(pfnzego_register_video_object_segmentation_state_changed_callback,     \
                             zego_register_video_object_segmentation_state_changed_callback)        \
    int loadLibraryInternal(const std::string &full_path) {                                         \
        if (nullptr != handle) {                                                                    \
            return 0;                                                                               \
        }                                                                                           \
        ZEGOEXP_LOAD_LIBRARY()                                                                      \
        do {                                                                                        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_add_publish_cdn_url,                              \
                                  zego_express_add_publish_cdn_url)                                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_add_publish_cdn_url_v2,                           \
                                  zego_express_add_publish_cdn_url_v2)                              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_ai_voice_changer_get_speaker_list,                \
                                  zego_express_ai_voice_changer_get_speaker_list)                   \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_ai_voice_changer_init,                            \
                                  zego_express_ai_voice_changer_init)                               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_ai_voice_changer_set_speaker,                     \
                                  zego_express_ai_voice_changer_set_speaker)                        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_ai_voice_changer_update,                          \
                                  zego_express_ai_voice_changer_update)                             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_audio_effect_player_get_current_progress,         \
                                  zego_express_audio_effect_player_get_current_progress)            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_audio_effect_player_get_total_duration,           \
                                  zego_express_audio_effect_player_get_total_duration)              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_audio_effect_player_load_resource,                \
                                  zego_express_audio_effect_player_load_resource)                   \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_audio_effect_player_pause,                        \
                                  zego_express_audio_effect_player_pause)                           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_audio_effect_player_pause_all,                    \
                                  zego_express_audio_effect_player_pause_all)                       \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_audio_effect_player_resume,                       \
                                  zego_express_audio_effect_player_resume)                          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_audio_effect_player_resume_all,                   \
                                  zego_express_audio_effect_player_resume_all)                      \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_audio_effect_player_seek_to,                      \
                                  zego_express_audio_effect_player_seek_to)                         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_audio_effect_player_set_play_speed,               \
                                  zego_express_audio_effect_player_set_play_speed)                  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_audio_effect_player_set_play_volume,              \
                                  zego_express_audio_effect_player_set_play_volume)                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_audio_effect_player_set_play_volume_all,          \
                                  zego_express_audio_effect_player_set_play_volume_all)             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_audio_effect_player_set_publish_volume,           \
                                  zego_express_audio_effect_player_set_publish_volume)              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_audio_effect_player_set_publish_volume_all,       \
                                  zego_express_audio_effect_player_set_publish_volume_all)          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_audio_effect_player_set_volume,                   \
                                  zego_express_audio_effect_player_set_volume)                      \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_audio_effect_player_set_volume_all,               \
                                  zego_express_audio_effect_player_set_volume_all)                  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_audio_effect_player_start,                        \
                                  zego_express_audio_effect_player_start)                           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_audio_effect_player_stop,                         \
                                  zego_express_audio_effect_player_stop)                            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_audio_effect_player_stop_all,                     \
                                  zego_express_audio_effect_player_stop_all)                        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_audio_effect_player_unload_resource,              \
                                  zego_express_audio_effect_player_unload_resource)                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_audio_effect_player_update_position,              \
                                  zego_express_audio_effect_player_update_position)                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_audio_vad_client_reset,                           \
                                  zego_express_audio_vad_client_reset)                              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_audio_vad_client_update,                          \
                                  zego_express_audio_vad_client_update)                             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_call_experimental_api,                            \
                                  zego_express_call_experimental_api)                               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_copyrighted_music_cancel_download,                \
                                  zego_express_copyrighted_music_cancel_download)                   \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_copyrighted_music_clear_cache,                    \
                                  zego_express_copyrighted_music_clear_cache)                       \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_copyrighted_music_download,                       \
                                  zego_express_copyrighted_music_download)                          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_copyrighted_music_get_average_score,              \
                                  zego_express_copyrighted_music_get_average_score)                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_copyrighted_music_get_cache_size,                 \
                                  zego_express_copyrighted_music_get_cache_size)                    \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_copyrighted_music_get_current_pitch,              \
                                  zego_express_copyrighted_music_get_current_pitch)                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_copyrighted_music_get_duration,                   \
                                  zego_express_copyrighted_music_get_duration)                      \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_copyrighted_music_get_full_score,                 \
                                  zego_express_copyrighted_music_get_full_score)                    \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_copyrighted_music_get_krc_lyric_by_token,         \
                                  zego_express_copyrighted_music_get_krc_lyric_by_token)            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_copyrighted_music_get_lrc_lyric,                  \
                                  zego_express_copyrighted_music_get_lrc_lyric)                     \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_copyrighted_music_get_lrc_lyric_with_config,      \
                                  zego_express_copyrighted_music_get_lrc_lyric_with_config)         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_copyrighted_music_get_lrc_lyric_with_vendor,      \
                                  zego_express_copyrighted_music_get_lrc_lyric_with_vendor)         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_copyrighted_music_get_music_by_token,             \
                                  zego_express_copyrighted_music_get_music_by_token)                \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_copyrighted_music_get_previous_score,             \
                                  zego_express_copyrighted_music_get_previous_score)                \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_copyrighted_music_get_shared_resource,            \
                                  zego_express_copyrighted_music_get_shared_resource)               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_copyrighted_music_get_shared_resource_v2,         \
                                  zego_express_copyrighted_music_get_shared_resource_v2)            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_copyrighted_music_get_standard_pitch,             \
                                  zego_express_copyrighted_music_get_standard_pitch)                \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_copyrighted_music_get_total_score,                \
                                  zego_express_copyrighted_music_get_total_score)                   \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_copyrighted_music_init,                           \
                                  zego_express_copyrighted_music_init)                              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_copyrighted_music_pause_score,                    \
                                  zego_express_copyrighted_music_pause_score)                       \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_copyrighted_music_query_cache,                    \
                                  zego_express_copyrighted_music_query_cache)                       \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_copyrighted_music_query_cache_with_config,        \
                                  zego_express_copyrighted_music_query_cache_with_config)           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_copyrighted_music_query_cache_with_config_v2,     \
                                  zego_express_copyrighted_music_query_cache_with_config_v2)        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_copyrighted_music_query_cache_with_vendor,        \
                                  zego_express_copyrighted_music_query_cache_with_vendor)           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_copyrighted_music_request_accompaniment,          \
                                  zego_express_copyrighted_music_request_accompaniment)             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_copyrighted_music_request_accompaniment_clip,     \
                                  zego_express_copyrighted_music_request_accompaniment_clip)        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_copyrighted_music_request_resource,               \
                                  zego_express_copyrighted_music_request_resource)                  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_copyrighted_music_request_resource_v2,            \
                                  zego_express_copyrighted_music_request_resource_v2)               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_copyrighted_music_request_song,                   \
                                  zego_express_copyrighted_music_request_song)                      \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_copyrighted_music_reset_score,                    \
                                  zego_express_copyrighted_music_reset_score)                       \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_copyrighted_music_resume_score,                   \
                                  zego_express_copyrighted_music_resume_score)                      \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_copyrighted_music_send_extended_request,          \
                                  zego_express_copyrighted_music_send_extended_request)             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_copyrighted_music_set_scoring_level,              \
                                  zego_express_copyrighted_music_set_scoring_level)                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_copyrighted_music_start_score,                    \
                                  zego_express_copyrighted_music_start_score)                       \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_copyrighted_music_stop_score,                     \
                                  zego_express_copyrighted_music_stop_score)                        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_create_ai_voice_changer,                          \
                                  zego_express_create_ai_voice_changer)                             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_create_audio_effect_player,                       \
                                  zego_express_create_audio_effect_player)                          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_create_audio_vad_client,                          \
                                  zego_express_create_audio_vad_client)                             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_create_copyrighted_music,                         \
                                  zego_express_create_copyrighted_music)                            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_create_media_data_publisher,                      \
                                  zego_express_create_media_data_publisher)                         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_create_media_player,                              \
                                  zego_express_create_media_player)                                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_create_range_audio,                               \
                                  zego_express_create_range_audio)                                  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_create_range_scene,                               \
                                  zego_express_create_range_scene)                                  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_create_real_time_sequential_data_manager,         \
                                  zego_express_create_real_time_sequential_data_manager)            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_create_screen_capture_source,                     \
                                  zego_express_create_screen_capture_source)                        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_custom_log, zego_express_custom_log)              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_destroy_ai_voice_changer,                         \
                                  zego_express_destroy_ai_voice_changer)                            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_destroy_audio_effect_player,                      \
                                  zego_express_destroy_audio_effect_player)                         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_destroy_audio_vad_client,                         \
                                  zego_express_destroy_audio_vad_client)                            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_destroy_copyrighted_music,                        \
                                  zego_express_destroy_copyrighted_music)                           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_destroy_media_data_publisher,                     \
                                  zego_express_destroy_media_data_publisher)                        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_destroy_media_player,                             \
                                  zego_express_destroy_media_player)                                \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_destroy_range_audio,                              \
                                  zego_express_destroy_range_audio)                                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_destroy_range_scene,                              \
                                  zego_express_destroy_range_scene)                                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_destroy_real_time_sequential_data_manager,        \
                                  zego_express_destroy_real_time_sequential_data_manager)           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_destroy_screen_capture_source,                    \
                                  zego_express_destroy_screen_capture_source)                       \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_aec, zego_express_enable_aec)              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_agc, zego_express_enable_agc)              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_aligned_audio_aux_data,                    \
                                  zego_express_enable_aligned_audio_aux_data)                       \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_alpha_channel_video_encoder,               \
                                  zego_express_enable_alpha_channel_video_encoder)                  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_ans, zego_express_enable_ans)              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_audio_capture_device,                      \
                                  zego_express_enable_audio_capture_device)                         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_audio_mixing,                              \
                                  zego_express_enable_audio_mixing)                                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_beautify, zego_express_enable_beautify)    \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_before_audio_prep_audio_data,              \
                                  zego_express_enable_before_audio_prep_audio_data)                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_camera, zego_express_enable_camera)        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_camera_adaptive_fps,                       \
                                  zego_express_enable_camera_adaptive_fps)                          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_captured_video_custom_video_render,        \
                                  zego_express_enable_captured_video_custom_video_render)           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_check_poc, zego_express_enable_check_poc)  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_color_enhancement,                         \
                                  zego_express_enable_color_enhancement)                            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_custom_audio_capture_processing,           \
                                  zego_express_enable_custom_audio_capture_processing)              \
            ZEGOEXP_LOAD_FUNC_PTR(                                                                  \
                pfnzego_express_enable_custom_audio_capture_processing_after_headphone_monitor,     \
                zego_express_enable_custom_audio_capture_processing_after_headphone_monitor)        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_custom_audio_io,                           \
                                  zego_express_enable_custom_audio_io)                              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_custom_audio_playback_processing,          \
                                  zego_express_enable_custom_audio_playback_processing)             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_custom_audio_remote_processing,            \
                                  zego_express_enable_custom_audio_remote_processing)               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_custom_video_capture,                      \
                                  zego_express_enable_custom_video_capture)                         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_custom_video_processing,                   \
                                  zego_express_enable_custom_video_processing)                      \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_custom_video_render,                       \
                                  zego_express_enable_custom_video_render)                          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_debug_assistant,                           \
                                  zego_express_enable_debug_assistant)                              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_effects_beauty,                            \
                                  zego_express_enable_effects_beauty)                               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_h_265_encode_fallback,                     \
                                  zego_express_enable_h_265_encode_fallback)                        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_hardware_decoder,                          \
                                  zego_express_enable_hardware_decoder)                             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_hardware_encoder,                          \
                                  zego_express_enable_hardware_encoder)                             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_headphone_aec,                             \
                                  zego_express_enable_headphone_aec)                                \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_headphone_monitor,                         \
                                  zego_express_enable_headphone_monitor)                            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_mix_engine_playout,                        \
                                  zego_express_enable_mix_engine_playout)                           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_mix_system_playout,                        \
                                  zego_express_enable_mix_system_playout)                           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_play_stream_virtual_stereo,                \
                                  zego_express_enable_play_stream_virtual_stereo)                   \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_publish_direct_to_cdn,                     \
                                  zego_express_enable_publish_direct_to_cdn)                        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_remote_video_custom_video_render,          \
                                  zego_express_enable_remote_video_custom_video_render)             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_speech_enhance,                            \
                                  zego_express_enable_speech_enhance)                               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_traffic_control,                           \
                                  zego_express_enable_traffic_control)                              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_traffic_control_by_channel,                \
                                  zego_express_enable_traffic_control_by_channel)                   \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_transient_ans,                             \
                                  zego_express_enable_transient_ans)                                \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_video_object_segmentation,                 \
                                  zego_express_enable_video_object_segmentation)                    \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_video_object_segmentation_with_config,     \
                                  zego_express_enable_video_object_segmentation_with_config)        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_video_super_resolution,                    \
                                  zego_express_enable_video_super_resolution)                       \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_enable_virtual_stereo,                            \
                                  zego_express_enable_virtual_stereo)                               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_engine_init, zego_express_engine_init)            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_engine_init_with_profile,                         \
                                  zego_express_engine_init_with_profile)                            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_engine_uninit_async,                              \
                                  zego_express_engine_uninit_async)                                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_fetch_custom_audio_render_pcm_data,               \
                                  zego_express_fetch_custom_audio_render_pcm_data)                  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_free_audio_device_list,                           \
                                  zego_express_free_audio_device_list)                              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_free_call_experimental_api_result,                \
                                  zego_express_free_call_experimental_api_result)                   \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_free_room_stream_list,                            \
                                  zego_express_free_room_stream_list)                               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_free_screen_capture_source_list,                  \
                                  zego_express_free_screen_capture_source_list)                     \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_free_video_device_list,                           \
                                  zego_express_free_video_device_list)                              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_get_android_context,                              \
                                  zego_express_get_android_context)                                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_get_audio_config, zego_express_get_audio_config)  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_get_audio_config_by_channel,                      \
                                  zego_express_get_audio_config_by_channel)                         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_get_audio_device_list,                            \
                                  zego_express_get_audio_device_list)                               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_get_audio_device_volume,                          \
                                  zego_express_get_audio_device_volume)                             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_get_audio_route_type,                             \
                                  zego_express_get_audio_route_type)                                \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_get_camera_max_zoom_factor,                       \
                                  zego_express_get_camera_max_zoom_factor)                          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_get_current_audio_device,                         \
                                  zego_express_get_current_audio_device)                            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_get_custom_video_capture_surface_texture,         \
                                  zego_express_get_custom_video_capture_surface_texture)            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_get_custom_video_process_output_surface_texture,  \
                                  zego_express_get_custom_video_process_output_surface_texture)     \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_get_default_audio_device_id,                      \
                                  zego_express_get_default_audio_device_id)                         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_get_default_video_device_id,                      \
                                  zego_express_get_default_video_device_id)                         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_get_network_time_info,                            \
                                  zego_express_get_network_time_info)                               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_get_room_stream_list,                             \
                                  zego_express_get_room_stream_list)                                \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_get_screen_capture_sources,                       \
                                  zego_express_get_screen_capture_sources)                          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_get_speaker_volume_in_app,                        \
                                  zego_express_get_speaker_volume_in_app)                           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_get_unity_captured_texture,                       \
                                  zego_express_get_unity_captured_texture)                          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_get_unity_played_texture,                         \
                                  zego_express_get_unity_played_texture)                            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_get_version, zego_express_get_version)            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_get_video_config, zego_express_get_video_config)  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_get_video_device_list,                            \
                                  zego_express_get_video_device_list)                               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_handle_api_call_result,                           \
                                  zego_express_handle_api_call_result)                              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_init_video_super_resolution,                      \
                                  zego_express_init_video_super_resolution)                         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_is_ai_voice_changer_supported,                    \
                                  zego_express_is_ai_voice_changer_supported)                       \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_is_audio_device_muted,                            \
                                  zego_express_is_audio_device_muted)                               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_is_camera_focus_supported,                        \
                                  zego_express_is_camera_focus_supported)                           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_is_feature_supported,                             \
                                  zego_express_is_feature_supported)                                \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_is_microphone_muted,                              \
                                  zego_express_is_microphone_muted)                                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_is_speaker_muted, zego_express_is_speaker_muted)  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_is_video_decoder_supported,                       \
                                  zego_express_is_video_decoder_supported)                          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_is_video_encoder_supported,                       \
                                  zego_express_is_video_encoder_supported)                          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_login_room, zego_express_login_room)              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_login_room_with_callback,                         \
                                  zego_express_login_room_with_callback)                            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_logout_all_room, zego_express_logout_all_room)    \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_logout_all_room_with_callback,                    \
                                  zego_express_logout_all_room_with_callback)                       \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_logout_room, zego_express_logout_room)            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_logout_room_with_callback,                        \
                                  zego_express_logout_room_with_callback)                           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_data_publisher_add_media_file_path,         \
                                  zego_express_media_data_publisher_add_media_file_path)            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_data_publisher_get_current_duration,        \
                                  zego_express_media_data_publisher_get_current_duration)           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_data_publisher_get_total_duration,          \
                                  zego_express_media_data_publisher_get_total_duration)             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_data_publisher_reset,                       \
                                  zego_express_media_data_publisher_reset)                          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_data_publisher_seek_to,                     \
                                  zego_express_media_data_publisher_seek_to)                        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_data_publisher_set_video_send_delay_time,   \
                                  zego_express_media_data_publisher_set_video_send_delay_time)      \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_clear_view,                          \
                                  zego_express_media_player_clear_view)                             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_enable_accurate_seek,                \
                                  zego_express_media_player_enable_accurate_seek)                   \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_enable_audio_data,                   \
                                  zego_express_media_player_enable_audio_data)                      \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_enable_aux,                          \
                                  zego_express_media_player_enable_aux)                             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_enable_block_data,                   \
                                  zego_express_media_player_enable_block_data)                      \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_enable_frequency_spectrum_monitor,   \
                                  zego_express_media_player_enable_frequency_spectrum_monitor)      \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_enable_live_audio_effect,            \
                                  zego_express_media_player_enable_live_audio_effect)               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_enable_local_cache,                  \
                                  zego_express_media_player_enable_local_cache)                     \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_enable_repeat,                       \
                                  zego_express_media_player_enable_repeat)                          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_enable_sound_level_monitor,          \
                                  zego_express_media_player_enable_sound_level_monitor)             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_enable_video_data,                   \
                                  zego_express_media_player_enable_video_data)                      \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_enable_view_mirror,                  \
                                  zego_express_media_player_enable_view_mirror)                     \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_enable_voice_changer,                \
                                  zego_express_media_player_enable_voice_changer)                   \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_get_audio_track_count,               \
                                  zego_express_media_player_get_audio_track_count)                  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_get_current_progress,                \
                                  zego_express_media_player_get_current_progress)                   \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_get_current_rendering_progress,      \
                                  zego_express_media_player_get_current_rendering_progress)         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_get_current_state,                   \
                                  zego_express_media_player_get_current_state)                      \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_get_media_info,                      \
                                  zego_express_media_player_get_media_info)                         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_get_network_resource_cache,          \
                                  zego_express_media_player_get_network_resource_cache)             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_get_play_volume,                     \
                                  zego_express_media_player_get_play_volume)                        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_get_playback_statistics,             \
                                  zego_express_media_player_get_playback_statistics)                \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_get_publish_volume,                  \
                                  zego_express_media_player_get_publish_volume)                     \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_get_total_duration,                  \
                                  zego_express_media_player_get_total_duration)                     \
            ZEGOEXP_LOAD_FUNC_PTR(                                                                  \
                pfnzego_express_media_player_load_copyrighted_music_resource_with_position,         \
                zego_express_media_player_load_copyrighted_music_resource_with_position)            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_load_resource,                       \
                                  zego_express_media_player_load_resource)                          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_load_resource_from_media_data,       \
                                  zego_express_media_player_load_resource_from_media_data)          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_load_resource_with_config,           \
                                  zego_express_media_player_load_resource_with_config)              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_load_resource_with_position,         \
                                  zego_express_media_player_load_resource_with_position)            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_mute_local_audio,                    \
                                  zego_express_media_player_mute_local_audio)                       \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_pause,                               \
                                  zego_express_media_player_pause)                                  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_resume,                              \
                                  zego_express_media_player_resume)                                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_seek_to,                             \
                                  zego_express_media_player_seek_to)                                \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_set_active_audio_channel,            \
                                  zego_express_media_player_set_active_audio_channel)               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_set_audio_track_index,               \
                                  zego_express_media_player_set_audio_track_index)                  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_set_audio_track_mode,                \
                                  zego_express_media_player_set_audio_track_mode)                   \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_set_audio_track_publish_index,       \
                                  zego_express_media_player_set_audio_track_publish_index)          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_set_http_header,                     \
                                  zego_express_media_player_set_http_header)                        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_set_network_buffer_threshold,        \
                                  zego_express_media_player_set_network_buffer_threshold)           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_set_network_resource_max_cache,      \
                                  zego_express_media_player_set_network_resource_max_cache)         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_set_play_loop_count,                 \
                                  zego_express_media_player_set_play_loop_count)                    \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_set_play_media_stream_type,          \
                                  zego_express_media_player_set_play_media_stream_type)             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_set_play_speed,                      \
                                  zego_express_media_player_set_play_speed)                         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_set_play_volume,                     \
                                  zego_express_media_player_set_play_volume)                        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_set_player_canvas,                   \
                                  zego_express_media_player_set_player_canvas)                      \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_set_progress_interval,               \
                                  zego_express_media_player_set_progress_interval)                  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_set_publish_volume,                  \
                                  zego_express_media_player_set_publish_volume)                     \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_set_voice_changer_param,             \
                                  zego_express_media_player_set_voice_changer_param)                \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_set_volume,                          \
                                  zego_express_media_player_set_volume)                             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_start,                               \
                                  zego_express_media_player_start)                                  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_stop,                                \
                                  zego_express_media_player_stop)                                   \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_take_snapshot,                       \
                                  zego_express_media_player_take_snapshot)                          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_media_player_update_position,                     \
                                  zego_express_media_player_update_position)                        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_mute_all_play_audio_streams,                      \
                                  zego_express_mute_all_play_audio_streams)                         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_mute_all_play_stream_audio,                       \
                                  zego_express_mute_all_play_stream_audio)                          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_mute_all_play_stream_video,                       \
                                  zego_express_mute_all_play_stream_video)                          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_mute_all_play_video_streams,                      \
                                  zego_express_mute_all_play_video_streams)                         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_mute_audio_device,                                \
                                  zego_express_mute_audio_device)                                   \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_mute_local_audio_mixing,                          \
                                  zego_express_mute_local_audio_mixing)                             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_mute_microphone, zego_express_mute_microphone)    \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_mute_play_stream_audio,                           \
                                  zego_express_mute_play_stream_audio)                              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_mute_play_stream_video,                           \
                                  zego_express_mute_play_stream_video)                              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_mute_publish_stream_audio,                        \
                                  zego_express_mute_publish_stream_audio)                           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_mute_publish_stream_video,                        \
                                  zego_express_mute_publish_stream_video)                           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_mute_speaker, zego_express_mute_speaker)          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_post_windows_message,                             \
                                  zego_express_post_windows_message)                                \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_range_audio_enable_microphone,                    \
                                  zego_express_range_audio_enable_microphone)                       \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_range_audio_enable_spatializer,                   \
                                  zego_express_range_audio_enable_spatializer)                      \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_range_audio_enable_speaker,                       \
                                  zego_express_range_audio_enable_speaker)                          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_range_audio_mute_user,                            \
                                  zego_express_range_audio_mute_user)                               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_range_audio_set_audio_receive_range,              \
                                  zego_express_range_audio_set_audio_receive_range)                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_range_audio_set_audio_receive_range_with_param,   \
                                  zego_express_range_audio_set_audio_receive_range_with_param)      \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_range_audio_set_audio_volume,                     \
                                  zego_express_range_audio_set_audio_volume)                        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_range_audio_set_position_update_frequency,        \
                                  zego_express_range_audio_set_position_update_frequency)           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_range_audio_set_stream_vocal_range,               \
                                  zego_express_range_audio_set_stream_vocal_range)                  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_range_audio_set_stream_vocal_range_with_param,    \
                                  zego_express_range_audio_set_stream_vocal_range_with_param)       \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_range_audio_set_team_id,                          \
                                  zego_express_range_audio_set_team_id)                             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_range_audio_update_audio_source,                  \
                                  zego_express_range_audio_update_audio_source)                     \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_range_audio_update_self_position,                 \
                                  zego_express_range_audio_update_self_position)                    \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_range_audio_update_stream_position,               \
                                  zego_express_range_audio_update_stream_position)                  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_range_scene_get_user_count,                       \
                                  zego_express_range_scene_get_user_count)                          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_range_scene_get_user_list_in_view,                \
                                  zego_express_range_scene_get_user_list_in_view)                   \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_range_scene_item_bind_item,                       \
                                  zego_express_range_scene_item_bind_item)                          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_range_scene_item_create_item,                     \
                                  zego_express_range_scene_item_create_item)                        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_range_scene_item_destroy_item,                    \
                                  zego_express_range_scene_item_destroy_item)                       \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_range_scene_item_unbind_item,                     \
                                  zego_express_range_scene_item_unbind_item)                        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_range_scene_item_update_item_command,             \
                                  zego_express_range_scene_item_update_item_command)                \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_range_scene_item_update_item_status,              \
                                  zego_express_range_scene_item_update_item_status)                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_range_scene_login_scene,                          \
                                  zego_express_range_scene_login_scene)                             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_range_scene_logout_scene,                         \
                                  zego_express_range_scene_logout_scene)                            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_range_scene_renew_token,                          \
                                  zego_express_range_scene_renew_token)                             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_range_scene_send_custom_command,                  \
                                  zego_express_range_scene_send_custom_command)                     \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_range_scene_set_stream_config,                    \
                                  zego_express_range_scene_set_stream_config)                       \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_range_scene_stream_enable_range_spatializer,      \
                                  zego_express_range_scene_stream_enable_range_spatializer)         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_range_scene_stream_mute_play_audio,               \
                                  zego_express_range_scene_stream_mute_play_audio)                  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_range_scene_stream_mute_play_video,               \
                                  zego_express_range_scene_stream_mute_play_video)                  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_range_scene_stream_set_receive_range,             \
                                  zego_express_range_scene_stream_set_receive_range)                \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_range_scene_stream_set_receive_range_with_param,  \
                                  zego_express_range_scene_stream_set_receive_range_with_param)     \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_range_scene_team_join_team,                       \
                                  zego_express_range_scene_team_join_team)                          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_range_scene_team_leave_team,                      \
                                  zego_express_range_scene_team_leave_team)                         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_range_scene_update_user_command,                  \
                                  zego_express_range_scene_update_user_command)                     \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_range_scene_update_user_position,                 \
                                  zego_express_range_scene_update_user_position)                    \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_range_scene_update_user_status,                   \
                                  zego_express_range_scene_update_user_status)                      \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_real_time_sequential_data_start_broadcasting,     \
                                  zego_express_real_time_sequential_data_start_broadcasting)        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_real_time_sequential_data_start_subscribing,      \
                                  zego_express_real_time_sequential_data_start_subscribing)         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_real_time_sequential_data_stop_broadcasting,      \
                                  zego_express_real_time_sequential_data_stop_broadcasting)         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_real_time_sequential_data_stop_subscribing,       \
                                  zego_express_real_time_sequential_data_stop_subscribing)          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_release_unity_captured_texture,                   \
                                  zego_express_release_unity_captured_texture)                      \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_release_unity_played_texture,                     \
                                  zego_express_release_unity_played_texture)                        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_remove_dump_data, zego_express_remove_dump_data)  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_remove_publish_cdn_url,                           \
                                  zego_express_remove_publish_cdn_url)                              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_renew_token, zego_express_renew_token)            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_reset_custom_video_capture_texture_context,       \
                                  zego_express_reset_custom_video_capture_texture_context)          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_screen_capture_enable_audio_capture,              \
                                  zego_express_screen_capture_enable_audio_capture)                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_screen_capture_enable_cursor_visible,             \
                                  zego_express_screen_capture_enable_cursor_visible)                \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_screen_capture_enable_window_activate,            \
                                  zego_express_screen_capture_enable_window_activate)               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_screen_capture_get_capture_source_rect,           \
                                  zego_express_screen_capture_get_capture_source_rect)              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_screen_capture_set_app_group_id_ios,              \
                                  zego_express_screen_capture_set_app_group_id_ios)                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_screen_capture_set_exclude_window_list,           \
                                  zego_express_screen_capture_set_exclude_window_list)              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_screen_capture_start_capture,                     \
                                  zego_express_screen_capture_start_capture)                        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_screen_capture_stop_capture,                      \
                                  zego_express_screen_capture_stop_capture)                         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_screen_capture_stop_capture_mobile,               \
                                  zego_express_screen_capture_stop_capture_mobile)                  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_screen_capture_update_capture_region,             \
                                  zego_express_screen_capture_update_capture_region)                \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_screen_capture_update_capture_source,             \
                                  zego_express_screen_capture_update_capture_source)                \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_screen_capture_update_publish_region,             \
                                  zego_express_screen_capture_update_publish_region)                \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_send_audio_side_info,                             \
                                  zego_express_send_audio_side_info)                                \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_send_barrage_message,                             \
                                  zego_express_send_barrage_message)                                \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_send_broadcast_message,                           \
                                  zego_express_send_broadcast_message)                              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_send_custom_audio_capture_aac_data,               \
                                  zego_express_send_custom_audio_capture_aac_data)                  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_send_custom_audio_capture_pcm_data,               \
                                  zego_express_send_custom_audio_capture_pcm_data)                  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_send_custom_command,                              \
                                  zego_express_send_custom_command)                                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_send_custom_video_capture_d3d_texture_data,       \
                                  zego_express_send_custom_video_capture_d3d_texture_data)          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_send_custom_video_capture_encoded_data,           \
                                  zego_express_send_custom_video_capture_encoded_data)              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_send_custom_video_capture_pixel_buffer,           \
                                  zego_express_send_custom_video_capture_pixel_buffer)              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_send_custom_video_capture_raw_data,               \
                                  zego_express_send_custom_video_capture_raw_data)                  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_send_custom_video_capture_texture_data,           \
                                  zego_express_send_custom_video_capture_texture_data)              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_send_custom_video_processed_cv_pixel_buffer,      \
                                  zego_express_send_custom_video_processed_cv_pixel_buffer)         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_send_custom_video_processed_cv_pixel_buffer_v2,   \
                                  zego_express_send_custom_video_processed_cv_pixel_buffer_v2)      \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_send_custom_video_processed_raw_data,             \
                                  zego_express_send_custom_video_processed_raw_data)                \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_send_custom_video_processed_raw_data_v2,          \
                                  zego_express_send_custom_video_processed_raw_data_v2)             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_send_custom_video_processed_texture_data,         \
                                  zego_express_send_custom_video_processed_texture_data)            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_send_custom_video_processed_texture_data_v2,      \
                                  zego_express_send_custom_video_processed_texture_data_v2)         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_send_real_time_sequential_data,                   \
                                  zego_express_send_real_time_sequential_data)                      \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_send_reference_audio_pcm_data,                    \
                                  zego_express_send_reference_audio_pcm_data)                       \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_send_sei, zego_express_send_sei)                  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_send_sei_sync_with_custom_video,                  \
                                  zego_express_send_sei_sync_with_custom_video)                     \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_send_transparent_message,                         \
                                  zego_express_send_transparent_message)                            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_aec_mode, zego_express_set_aec_mode)          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_all_play_stream_volume,                       \
                                  zego_express_set_all_play_stream_volume)                          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_android_env, zego_express_set_android_env)    \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_ans_mode, zego_express_set_ans_mode)          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_app_orientation,                              \
                                  zego_express_set_app_orientation)                                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_app_orientation_mode,                         \
                                  zego_express_set_app_orientation_mode)                            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_audio_capture_stereo_mode,                    \
                                  zego_express_set_audio_capture_stereo_mode)                       \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_audio_config, zego_express_set_audio_config)  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_audio_config_by_channel,                      \
                                  zego_express_set_audio_config_by_channel)                         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_audio_device_mode,                            \
                                  zego_express_set_audio_device_mode)                               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_audio_device_volume,                          \
                                  zego_express_set_audio_device_volume)                             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_audio_equalizer_gain,                         \
                                  zego_express_set_audio_equalizer_gain)                            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_audio_mixing_volume,                          \
                                  zego_express_set_audio_mixing_volume)                             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_audio_mixing_volume_with_type,                \
                                  zego_express_set_audio_mixing_volume_with_type)                   \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_audio_route_to_speaker,                       \
                                  zego_express_set_audio_route_to_speaker)                          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_audio_source, zego_express_set_audio_source)  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_audio_source_with_config,                     \
                                  zego_express_set_audio_source_with_config)                        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_beautify_option,                              \
                                  zego_express_set_beautify_option)                                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_camera_exposure_compensation,                 \
                                  zego_express_set_camera_exposure_compensation)                    \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_camera_exposure_mode,                         \
                                  zego_express_set_camera_exposure_mode)                            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_camera_exposure_point_in_preview,             \
                                  zego_express_set_camera_exposure_point_in_preview)                \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_camera_focus_mode,                            \
                                  zego_express_set_camera_focus_mode)                               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_camera_focus_point_in_preview,                \
                                  zego_express_set_camera_focus_point_in_preview)                   \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_camera_stabilization_mode,                    \
                                  zego_express_set_camera_stabilization_mode)                       \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_camera_zoom_factor,                           \
                                  zego_express_set_camera_zoom_factor)                              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_capture_pipeline_scale_mode,                  \
                                  zego_express_set_capture_pipeline_scale_mode)                     \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_capture_volume,                               \
                                  zego_express_set_capture_volume)                                  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_cloud_proxy_config,                           \
                                  zego_express_set_cloud_proxy_config)                              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_custom_video_capture_device_state,            \
                                  zego_express_set_custom_video_capture_device_state)               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_custom_video_capture_fill_mode,               \
                                  zego_express_set_custom_video_capture_fill_mode)                  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_custom_video_capture_flip_mode,               \
                                  zego_express_set_custom_video_capture_flip_mode)                  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_custom_video_capture_region_of_interest,      \
                                  zego_express_set_custom_video_capture_region_of_interest)         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_custom_video_capture_rotation,                \
                                  zego_express_set_custom_video_capture_rotation)                   \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_custom_video_capture_transform_matrix,        \
                                  zego_express_set_custom_video_capture_transform_matrix)           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_dummy_capture_image_path,                     \
                                  zego_express_set_dummy_capture_image_path)                        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_effects_beauty_param,                         \
                                  zego_express_set_effects_beauty_param)                            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_electronic_effects,                           \
                                  zego_express_set_electronic_effects)                              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_engine_config,                                \
                                  zego_express_set_engine_config)                                   \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_geo_fence, zego_express_set_geo_fence)        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_headphone_monitor_volume,                     \
                                  zego_express_set_headphone_monitor_volume)                        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_ios_app_orientation,                          \
                                  zego_express_set_ios_app_orientation)                             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_license, zego_express_set_license)            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_local_proxy_config,                           \
                                  zego_express_set_local_proxy_config)                              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_log_config, zego_express_set_log_config)      \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_low_light_enhancement,                        \
                                  zego_express_set_low_light_enhancement)                           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_min_video_bitrate_for_traffic_control,        \
                                  zego_express_set_min_video_bitrate_for_traffic_control)           \
            ZEGOEXP_LOAD_FUNC_PTR(                                                                  \
                pfnzego_express_set_min_video_bitrate_for_traffic_control_by_channel,               \
                zego_express_set_min_video_bitrate_for_traffic_control_by_channel)                  \
            ZEGOEXP_LOAD_FUNC_PTR(                                                                  \
                pfnzego_express_set_min_video_fps_for_traffic_control_by_channel,                   \
                zego_express_set_min_video_fps_for_traffic_control_by_channel)                      \
            ZEGOEXP_LOAD_FUNC_PTR(                                                                  \
                pfnzego_express_set_min_video_resolution_for_traffic_control_by_channel,            \
                zego_express_set_min_video_resolution_for_traffic_control_by_channel)               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_mix_system_playout_volume,                    \
                                  zego_express_set_mix_system_playout_volume)                       \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_platform_language,                            \
                                  zego_express_set_platform_language)                               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_play_stream_buffer_interval_range,            \
                                  zego_express_set_play_stream_buffer_interval_range)               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_play_stream_cross_app_info,                   \
                                  zego_express_set_play_stream_cross_app_info)                      \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_play_stream_decryption_key,                   \
                                  zego_express_set_play_stream_decryption_key)                      \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_play_stream_focus_on,                         \
                                  zego_express_set_play_stream_focus_on)                            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_play_stream_video_type,                       \
                                  zego_express_set_play_stream_video_type)                          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_play_streams_alignment_property,              \
                                  zego_express_set_play_streams_alignment_property)                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_play_volume, zego_express_set_play_volume)    \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_publish_dual_stream_config,                   \
                                  zego_express_set_publish_dual_stream_config)                      \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_publish_stream_encryption_key,                \
                                  zego_express_set_publish_stream_encryption_key)                   \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_publish_watermark,                            \
                                  zego_express_set_publish_watermark)                               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_range_audio_custom_mode,                      \
                                  zego_express_set_range_audio_custom_mode)                         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_range_audio_mode,                             \
                                  zego_express_set_range_audio_mode)                                \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_reverb_advanced_param,                        \
                                  zego_express_set_reverb_advanced_param)                           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_reverb_echo_param,                            \
                                  zego_express_set_reverb_echo_param)                               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_reverb_preset,                                \
                                  zego_express_set_reverb_preset)                                   \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_room_extra_info,                              \
                                  zego_express_set_room_extra_info)                                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_room_mode, zego_express_set_room_mode)        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_room_scenario,                                \
                                  zego_express_set_room_scenario)                                   \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_sei_config, zego_express_set_sei_config)      \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_speaker_volume_in_app,                        \
                                  zego_express_set_speaker_volume_in_app)                           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_stream_alignment_property,                    \
                                  zego_express_set_stream_alignment_property)                       \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_stream_extra_info,                            \
                                  zego_express_set_stream_extra_info)                               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_traffic_control_focus_on,                     \
                                  zego_express_set_traffic_control_focus_on)                        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_traffic_control_focus_on_by_channel,          \
                                  zego_express_set_traffic_control_focus_on_by_channel)             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_video_config, zego_express_set_video_config)  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_video_mirror_mode,                            \
                                  zego_express_set_video_mirror_mode)                               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_video_source, zego_express_set_video_source)  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_voice_changer_param,                          \
                                  zego_express_set_voice_changer_param)                             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_set_voice_changer_preset,                         \
                                  zego_express_set_voice_changer_preset)                            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_start_audio_data_observer,                        \
                                  zego_express_start_audio_data_observer)                           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_start_audio_device_volume_monitor,                \
                                  zego_express_start_audio_device_volume_monitor)                   \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_start_audio_spectrum_monitor,                     \
                                  zego_express_start_audio_spectrum_monitor)                        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_start_audio_vad_stable_state_monitor,             \
                                  zego_express_start_audio_vad_stable_state_monitor)                \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_start_auto_mixer_task,                            \
                                  zego_express_start_auto_mixer_task)                               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_start_dump_data, zego_express_start_dump_data)    \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_start_effects_env,                                \
                                  zego_express_start_effects_env)                                   \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_start_mixer_task, zego_express_start_mixer_task)  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_start_network_probe,                              \
                                  zego_express_start_network_probe)                                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_start_network_speed_test,                         \
                                  zego_express_start_network_speed_test)                            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_start_performance_monitor,                        \
                                  zego_express_start_performance_monitor)                           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_start_playing_stream,                             \
                                  zego_express_start_playing_stream)                                \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_start_playing_stream_in_scene,                    \
                                  zego_express_start_playing_stream_in_scene)                       \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_start_playing_stream_with_config,                 \
                                  zego_express_start_playing_stream_with_config)                    \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_start_preview, zego_express_start_preview)        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_start_publishing_stream,                          \
                                  zego_express_start_publishing_stream)                             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_start_publishing_stream_in_scene,                 \
                                  zego_express_start_publishing_stream_in_scene)                    \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_start_publishing_stream_with_config,              \
                                  zego_express_start_publishing_stream_with_config)                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_start_recording_captured_data,                    \
                                  zego_express_start_recording_captured_data)                       \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_start_screen_capture_in_app_ios,                  \
                                  zego_express_start_screen_capture_in_app_ios)                     \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_start_screen_capture_mobile,                      \
                                  zego_express_start_screen_capture_mobile)                         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_start_sound_level_monitor,                        \
                                  zego_express_start_sound_level_monitor)                           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_start_sound_level_monitor_with_config,            \
                                  zego_express_start_sound_level_monitor_with_config)               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_stop_audio_data_observer,                         \
                                  zego_express_stop_audio_data_observer)                            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_stop_audio_device_volume_monitor,                 \
                                  zego_express_stop_audio_device_volume_monitor)                    \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_stop_audio_spectrum_monitor,                      \
                                  zego_express_stop_audio_spectrum_monitor)                         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_stop_audio_vad_stable_state_monitor,              \
                                  zego_express_stop_audio_vad_stable_state_monitor)                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_stop_auto_mixer_task,                             \
                                  zego_express_stop_auto_mixer_task)                                \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_stop_dump_data, zego_express_stop_dump_data)      \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_stop_effects_env, zego_express_stop_effects_env)  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_stop_mixer_task, zego_express_stop_mixer_task)    \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_stop_network_probe,                               \
                                  zego_express_stop_network_probe)                                  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_stop_network_speed_test,                          \
                                  zego_express_stop_network_speed_test)                             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_stop_performance_monitor,                         \
                                  zego_express_stop_performance_monitor)                            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_stop_playing_stream,                              \
                                  zego_express_stop_playing_stream)                                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_stop_preview, zego_express_stop_preview)          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_stop_publishing_stream,                           \
                                  zego_express_stop_publishing_stream)                              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_stop_recording_captured_data,                     \
                                  zego_express_stop_recording_captured_data)                        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_stop_sound_level_monitor,                         \
                                  zego_express_stop_sound_level_monitor)                            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_switch_playing_stream,                            \
                                  zego_express_switch_playing_stream)                               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_switch_room, zego_express_switch_room)            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_take_play_stream_snapshot,                        \
                                  zego_express_take_play_stream_snapshot)                           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_take_publish_stream_snapshot,                     \
                                  zego_express_take_publish_stream_snapshot)                        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_test_network_connectivity,                        \
                                  zego_express_test_network_connectivity)                           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_uninit_video_super_resolution,                    \
                                  zego_express_uninit_video_super_resolution)                       \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_update_playing_canvas,                            \
                                  zego_express_update_playing_canvas)                               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_update_screen_capture_config_mobile,              \
                                  zego_express_update_screen_capture_config_mobile)                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_upload_dump_data, zego_express_upload_dump_data)  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_upload_log, zego_express_upload_log)              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_use_audio_device, zego_express_use_audio_device)  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_use_front_camera, zego_express_use_front_camera)  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_express_use_video_device, zego_express_use_video_device)  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_ai_voice_changer_event_callback,                 \
                                  zego_register_ai_voice_changer_event_callback)                    \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_ai_voice_changer_get_speaker_list_callback,      \
                                  zego_register_ai_voice_changer_get_speaker_list_callback)         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_ai_voice_changer_init_callback,                  \
                                  zego_register_ai_voice_changer_init_callback)                     \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_ai_voice_changer_set_speaker_callback,           \
                                  zego_register_ai_voice_changer_set_speaker_callback)              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_ai_voice_changer_update_callback,                \
                                  zego_register_ai_voice_changer_update_callback)                   \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_ai_voice_changer_update_progress_callback,       \
                                  zego_register_ai_voice_changer_update_progress_callback)          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_aligned_audio_aux_data_callback,                 \
                                  zego_register_aligned_audio_aux_data_callback)                    \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_api_called_result_callback,                      \
                                  zego_register_api_called_result_callback)                         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_audio_device_state_changed_callback,             \
                                  zego_register_audio_device_state_changed_callback)                \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_audio_device_volume_changed_callback,            \
                                  zego_register_audio_device_volume_changed_callback)               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_audio_effect_play_state_update_callback,         \
                                  zego_register_audio_effect_play_state_update_callback)            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_audio_effect_player_load_resource_callback,      \
                                  zego_register_audio_effect_player_load_resource_callback)         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_audio_effect_player_seek_to_callback,            \
                                  zego_register_audio_effect_player_seek_to_callback)               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_audio_route_change_callback,                     \
                                  zego_register_audio_route_change_callback)                        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_audio_vad_state_update_callback,                 \
                                  zego_register_audio_vad_state_update_callback)                    \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_auto_mixer_sound_level_update_callback,          \
                                  zego_register_auto_mixer_sound_level_update_callback)             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_auto_mixer_start_result_callback,                \
                                  zego_register_auto_mixer_start_result_callback)                   \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_auto_mixer_stop_result_callback,                 \
                                  zego_register_auto_mixer_stop_result_callback)                    \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_before_audio_prep_audio_data_callback,           \
                                  zego_register_before_audio_prep_audio_data_callback)              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_captured_audio_data_callback,                    \
                                  zego_register_captured_audio_data_callback)                       \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_captured_audio_spectrum_update_callback,         \
                                  zego_register_captured_audio_spectrum_update_callback)            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_captured_data_record_progress_update_callback,   \
                                  zego_register_captured_data_record_progress_update_callback)      \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_captured_data_record_state_update_callback,      \
                                  zego_register_captured_data_record_state_update_callback)         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_captured_sound_level_info_update_callback,       \
                                  zego_register_captured_sound_level_info_update_callback)          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_captured_sound_level_update_callback,            \
                                  zego_register_captured_sound_level_update_callback)               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_copy_audio_mixing_data_callback,                 \
                                  zego_register_copy_audio_mixing_data_callback)                    \
            ZEGOEXP_LOAD_FUNC_PTR(                                                                  \
                pfnzego_register_copyrighted_music_current_pitch_value_update_callback,             \
                zego_register_copyrighted_music_current_pitch_value_update_callback)                \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_copyrighted_music_download_callback,             \
                                  zego_register_copyrighted_music_download_callback)                \
            ZEGOEXP_LOAD_FUNC_PTR(                                                                  \
                pfnzego_register_copyrighted_music_download_progress_update_callback,               \
                zego_register_copyrighted_music_download_progress_update_callback)                  \
            ZEGOEXP_LOAD_FUNC_PTR(                                                                  \
                pfnzego_register_copyrighted_music_get_krc_lyric_by_token_callback,                 \
                zego_register_copyrighted_music_get_krc_lyric_by_token_callback)                    \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_copyrighted_music_get_lrc_lyric_callback,        \
                                  zego_register_copyrighted_music_get_lrc_lyric_callback)           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_copyrighted_music_get_music_by_token_callback,   \
                                  zego_register_copyrighted_music_get_music_by_token_callback)      \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_copyrighted_music_get_shared_resource_callback,  \
                                  zego_register_copyrighted_music_get_shared_resource_callback)     \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_copyrighted_music_get_standard_pitch_callback,   \
                                  zego_register_copyrighted_music_get_standard_pitch_callback)      \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_copyrighted_music_init_callback,                 \
                                  zego_register_copyrighted_music_init_callback)                    \
            ZEGOEXP_LOAD_FUNC_PTR(                                                                  \
                pfnzego_register_copyrighted_music_request_accompaniment_callback,                  \
                zego_register_copyrighted_music_request_accompaniment_callback)                     \
            ZEGOEXP_LOAD_FUNC_PTR(                                                                  \
                pfnzego_register_copyrighted_music_request_accompaniment_clip_callback,             \
                zego_register_copyrighted_music_request_accompaniment_clip_callback)                \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_copyrighted_music_request_resource_callback,     \
                                  zego_register_copyrighted_music_request_resource_callback)        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_copyrighted_music_request_song_callback,         \
                                  zego_register_copyrighted_music_request_song_callback)            \
            ZEGOEXP_LOAD_FUNC_PTR(                                                                  \
                pfnzego_register_copyrighted_music_send_extended_request_callback,                  \
                zego_register_copyrighted_music_send_extended_request_callback)                     \
            ZEGOEXP_LOAD_FUNC_PTR(                                                                  \
                pfnzego_register_custom_video_capture_encoded_data_traffic_control_callback,        \
                zego_register_custom_video_capture_encoded_data_traffic_control_callback)           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_custom_video_capture_frame_rate_callback,        \
                                  zego_register_custom_video_capture_frame_rate_callback)           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_custom_video_capture_start_callback,             \
                                  zego_register_custom_video_capture_start_callback)                \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_custom_video_capture_stop_callback,              \
                                  zego_register_custom_video_capture_stop_callback)                 \
            ZEGOEXP_LOAD_FUNC_PTR(                                                                  \
                pfnzego_register_custom_video_process_captured_unprocessed_cvpixelbuffer_callback,  \
                zego_register_custom_video_process_captured_unprocessed_cvpixelbuffer_callback)     \
            ZEGOEXP_LOAD_FUNC_PTR(                                                                  \
                pfnzego_register_custom_video_process_captured_unprocessed_raw_data_callback,       \
                zego_register_custom_video_process_captured_unprocessed_raw_data_callback)          \
            ZEGOEXP_LOAD_FUNC_PTR(                                                                  \
                pfnzego_register_custom_video_process_captured_unprocessed_texture_data_callback,   \
                zego_register_custom_video_process_captured_unprocessed_texture_data_callback)      \
            ZEGOEXP_LOAD_FUNC_PTR(                                                                  \
                pfnzego_register_custom_video_process_get_input_surface_texture_callback,           \
                zego_register_custom_video_process_get_input_surface_texture_callback)              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_custom_video_process_start_callback,             \
                                  zego_register_custom_video_process_start_callback)                \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_custom_video_process_stop_callback,              \
                                  zego_register_custom_video_process_stop_callback)                 \
            ZEGOEXP_LOAD_FUNC_PTR(                                                                  \
                pfnzego_register_custom_video_render_captured_frame_data_callback,                  \
                zego_register_custom_video_render_captured_frame_data_callback)                     \
            ZEGOEXP_LOAD_FUNC_PTR(                                                                  \
                pfnzego_register_custom_video_render_captured_frame_pixel_buffer_callback,          \
                zego_register_custom_video_render_captured_frame_pixel_buffer_callback)             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_custom_video_render_remote_frame_data_callback,  \
                                  zego_register_custom_video_render_remote_frame_data_callback)     \
            ZEGOEXP_LOAD_FUNC_PTR(                                                                  \
                pfnzego_register_custom_video_render_remote_frame_encoded_data_callback,            \
                zego_register_custom_video_render_remote_frame_encoded_data_callback)               \
            ZEGOEXP_LOAD_FUNC_PTR(                                                                  \
                pfnzego_register_custom_video_render_remote_frame_pixel_buffer_callback,            \
                zego_register_custom_video_render_remote_frame_pixel_buffer_callback)               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_debug_error_callback,                            \
                                  zego_register_debug_error_callback)                               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_engine_state_update_callback,                    \
                                  zego_register_engine_state_update_callback)                       \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_engine_uninit_callback,                          \
                                  zego_register_engine_uninit_callback)                             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_fatal_error_callback,                            \
                                  zego_register_fatal_error_callback)                               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_im_recv_barrage_message_callback,                \
                                  zego_register_im_recv_barrage_message_callback)                   \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_im_recv_broadcast_message_callback,              \
                                  zego_register_im_recv_broadcast_message_callback)                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_im_recv_custom_command_callback,                 \
                                  zego_register_im_recv_custom_command_callback)                    \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_im_send_barrage_message_result_callback,         \
                                  zego_register_im_send_barrage_message_result_callback)            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_im_send_broadcast_message_result_callback,       \
                                  zego_register_im_send_broadcast_message_result_callback)          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_im_send_custom_command_result_callback,          \
                                  zego_register_im_send_custom_command_result_callback)             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_local_device_exception_occurred_callback,        \
                                  zego_register_local_device_exception_occurred_callback)           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_media_data_publisher_file_close_callback,        \
                                  zego_register_media_data_publisher_file_close_callback)           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_media_data_publisher_file_data_begin_callback,   \
                                  zego_register_media_data_publisher_file_data_begin_callback)      \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_media_data_publisher_file_data_end_callback,     \
                                  zego_register_media_data_publisher_file_data_end_callback)        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_media_data_publisher_file_open_callback,         \
                                  zego_register_media_data_publisher_file_open_callback)            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_media_player_audio_frame_callback,               \
                                  zego_register_media_player_audio_frame_callback)                  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_media_player_block_begin_callback,               \
                                  zego_register_media_player_block_begin_callback)                  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_media_player_block_data_callback,                \
                                  zego_register_media_player_block_data_callback)                   \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_media_player_first_frame_event_callback,         \
                                  zego_register_media_player_first_frame_event_callback)            \
            ZEGOEXP_LOAD_FUNC_PTR(                                                                  \
                pfnzego_register_media_player_frequency_spectrum_update_callback,                   \
                zego_register_media_player_frequency_spectrum_update_callback)                      \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_media_player_load_resource_callback,             \
                                  zego_register_media_player_load_resource_callback)                \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_media_player_local_cache_callback,               \
                                  zego_register_media_player_local_cache_callback)                  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_media_player_network_event_callback,             \
                                  zego_register_media_player_network_event_callback)                \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_media_player_playing_progress_callback,          \
                                  zego_register_media_player_playing_progress_callback)             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_media_player_recv_sei_callback,                  \
                                  zego_register_media_player_recv_sei_callback)                     \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_media_player_rendering_progress_callback,        \
                                  zego_register_media_player_rendering_progress_callback)           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_media_player_seek_to_callback,                   \
                                  zego_register_media_player_seek_to_callback)                      \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_media_player_sound_level_update_callback,        \
                                  zego_register_media_player_sound_level_update_callback)           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_media_player_state_update_callback,              \
                                  zego_register_media_player_state_update_callback)                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_media_player_take_snapshot_result_callback,      \
                                  zego_register_media_player_take_snapshot_result_callback)         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_media_player_video_frame_callback,               \
                                  zego_register_media_player_video_frame_callback)                  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_media_player_video_size_changed_callback,        \
                                  zego_register_media_player_video_size_changed_callback)           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_mixed_audio_data_callback,                       \
                                  zego_register_mixed_audio_data_callback)                          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_mixer_relay_cdn_state_update_callback,           \
                                  zego_register_mixer_relay_cdn_state_update_callback)              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_mixer_sound_level_update_callback,               \
                                  zego_register_mixer_sound_level_update_callback)                  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_mixer_start_result_callback,                     \
                                  zego_register_mixer_start_result_callback)                        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_mixer_stop_result_callback,                      \
                                  zego_register_mixer_stop_result_callback)                         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_network_mode_changed_callback,                   \
                                  zego_register_network_mode_changed_callback)                      \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_network_probe_result_callback,                   \
                                  zego_register_network_probe_result_callback)                      \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_network_quality_callback,                        \
                                  zego_register_network_quality_callback)                           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_network_speed_test_error_callback,               \
                                  zego_register_network_speed_test_error_callback)                  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_network_speed_test_quality_update_callback,      \
                                  zego_register_network_speed_test_quality_update_callback)         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_network_time_synchronized_callback,              \
                                  zego_register_network_time_synchronized_callback)                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_performance_status_update_callback,              \
                                  zego_register_performance_status_update_callback)                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_playback_audio_data_callback,                    \
                                  zego_register_playback_audio_data_callback)                       \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_player_audio_data_callback,                      \
                                  zego_register_player_audio_data_callback)                         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_player_low_fps_warning_callback,                 \
                                  zego_register_player_low_fps_warning_callback)                    \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_player_media_event_callback,                     \
                                  zego_register_player_media_event_callback)                        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_player_quality_update_callback,                  \
                                  zego_register_player_quality_update_callback)                     \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_player_recv_audio_first_frame_callback,          \
                                  zego_register_player_recv_audio_first_frame_callback)             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_player_recv_audio_side_info_callback,            \
                                  zego_register_player_recv_audio_side_info_callback)               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_player_recv_media_side_info_callback,            \
                                  zego_register_player_recv_media_side_info_callback)               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_player_recv_sei_callback,                        \
                                  zego_register_player_recv_sei_callback)                           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_player_recv_video_first_frame_callback,          \
                                  zego_register_player_recv_video_first_frame_callback)             \
            ZEGOEXP_LOAD_FUNC_PTR(                                                                  \
                pfnzego_register_player_render_camera_video_first_frame_callback,                   \
                zego_register_player_render_camera_video_first_frame_callback)                      \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_player_render_video_first_frame_callback,        \
                                  zego_register_player_render_video_first_frame_callback)           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_player_state_update_callback,                    \
                                  zego_register_player_state_update_callback)                       \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_player_stream_event_callback,                    \
                                  zego_register_player_stream_event_callback)                       \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_player_switched_callback,                        \
                                  zego_register_player_switched_callback)                           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_player_take_snapshot_result_callback,            \
                                  zego_register_player_take_snapshot_result_callback)               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_player_video_size_changed_callback,              \
                                  zego_register_player_video_size_changed_callback)                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_player_video_super_resolution_update_callback,   \
                                  zego_register_player_video_super_resolution_update_callback)      \
            ZEGOEXP_LOAD_FUNC_PTR(                                                                  \
                pfnzego_register_process_captured_audio_data_after_used_headphone_monitor_callback, \
                zego_register_process_captured_audio_data_after_used_headphone_monitor_callback)    \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_process_captured_audio_data_callback,            \
                                  zego_register_process_captured_audio_data_callback)               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_process_playback_audio_data_callback,            \
                                  zego_register_process_playback_audio_data_callback)               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_process_remote_audio_data_callback,              \
                                  zego_register_process_remote_audio_data_callback)                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_publisher_captured_audio_first_frame_callback,   \
                                  zego_register_publisher_captured_audio_first_frame_callback)      \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_publisher_captured_video_first_frame_callback,   \
                                  zego_register_publisher_captured_video_first_frame_callback)      \
            ZEGOEXP_LOAD_FUNC_PTR(                                                                  \
                pfnzego_register_publisher_dummy_capture_image_path_error_callback,                 \
                zego_register_publisher_dummy_capture_image_path_error_callback)                    \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_publisher_low_fps_warning_callback,              \
                                  zego_register_publisher_low_fps_warning_callback)                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_publisher_quality_update_callback,               \
                                  zego_register_publisher_quality_update_callback)                  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_publisher_relay_cdn_state_update_callback,       \
                                  zego_register_publisher_relay_cdn_state_update_callback)          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_publisher_render_video_first_frame_callback,     \
                                  zego_register_publisher_render_video_first_frame_callback)        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_publisher_send_audio_first_frame_callback,       \
                                  zego_register_publisher_send_audio_first_frame_callback)          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_publisher_send_video_first_frame_callback,       \
                                  zego_register_publisher_send_video_first_frame_callback)          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_publisher_state_update_callback,                 \
                                  zego_register_publisher_state_update_callback)                    \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_publisher_stream_event_callback,                 \
                                  zego_register_publisher_stream_event_callback)                    \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_publisher_take_snapshot_result_callback,         \
                                  zego_register_publisher_take_snapshot_result_callback)            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_publisher_update_cdn_url_result_callback,        \
                                  zego_register_publisher_update_cdn_url_result_callback)           \
            ZEGOEXP_LOAD_FUNC_PTR(                                                                  \
                pfnzego_register_publisher_update_stream_extra_info_result_callback,                \
                zego_register_publisher_update_stream_extra_info_result_callback)                   \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_publisher_video_encoder_changed_callback,        \
                                  zego_register_publisher_video_encoder_changed_callback)           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_publisher_video_size_changed_callback,           \
                                  zego_register_publisher_video_size_changed_callback)              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_range_audio_microphone_state_update_callback,    \
                                  zego_register_range_audio_microphone_state_update_callback)       \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_range_scene_custom_command_update_callback,      \
                                  zego_register_range_scene_custom_command_update_callback)         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_range_scene_enter_view_callback,                 \
                                  zego_register_range_scene_enter_view_callback)                    \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_range_scene_get_user_count_callback,             \
                                  zego_register_range_scene_get_user_count_callback)                \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_range_scene_get_user_list_in_view_callback,      \
                                  zego_register_range_scene_get_user_list_in_view_callback)         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_range_scene_item_bind_item_callback,             \
                                  zego_register_range_scene_item_bind_item_callback)                \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_range_scene_item_create_item_callback,           \
                                  zego_register_range_scene_item_create_item_callback)              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_range_scene_item_destroy_item_callback,          \
                                  zego_register_range_scene_item_destroy_item_callback)             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_range_scene_item_item_bind_update_callback,      \
                                  zego_register_range_scene_item_item_bind_update_callback)         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_range_scene_item_item_command_update_callback,   \
                                  zego_register_range_scene_item_item_command_update_callback)      \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_range_scene_item_item_enter_view_callback,       \
                                  zego_register_range_scene_item_item_enter_view_callback)          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_range_scene_item_item_leave_view_callback,       \
                                  zego_register_range_scene_item_item_leave_view_callback)          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_range_scene_item_item_status_update_callback,    \
                                  zego_register_range_scene_item_item_status_update_callback)       \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_range_scene_item_item_unbind_update_callback,    \
                                  zego_register_range_scene_item_item_unbind_update_callback)       \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_range_scene_item_unbind_item_callback,           \
                                  zego_register_range_scene_item_unbind_item_callback)              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_range_scene_item_update_item_command_callback,   \
                                  zego_register_range_scene_item_update_item_command_callback)      \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_range_scene_item_update_item_status_callback,    \
                                  zego_register_range_scene_item_update_item_status_callback)       \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_range_scene_leave_view_callback,                 \
                                  zego_register_range_scene_leave_view_callback)                    \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_range_scene_login_scene_callback,                \
                                  zego_register_range_scene_login_scene_callback)                   \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_range_scene_logout_scene_callback,               \
                                  zego_register_range_scene_logout_scene_callback)                  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_range_scene_scene_state_update_callback,         \
                                  zego_register_range_scene_scene_state_update_callback)            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_range_scene_send_custom_command_callback,        \
                                  zego_register_range_scene_send_custom_command_callback)           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_range_scene_stream_user_camera_update_callback,  \
                                  zego_register_range_scene_stream_user_camera_update_callback)     \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_range_scene_stream_user_mic_update_callback,     \
                                  zego_register_range_scene_stream_user_mic_update_callback)        \
            ZEGOEXP_LOAD_FUNC_PTR(                                                                  \
                pfnzego_register_range_scene_stream_user_speaker_update_callback,                   \
                zego_register_range_scene_stream_user_speaker_update_callback)                      \
            ZEGOEXP_LOAD_FUNC_PTR(                                                                  \
                pfnzego_register_range_scene_stream_user_stream_state_update_callback,              \
                zego_register_range_scene_stream_user_stream_state_update_callback)                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_range_scene_team_join_team_callback,             \
                                  zego_register_range_scene_team_join_team_callback)                \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_range_scene_team_leave_team_callback,            \
                                  zego_register_range_scene_team_leave_team_callback)               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_range_scene_team_team_member_update_callback,    \
                                  zego_register_range_scene_team_team_member_update_callback)       \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_range_scene_team_team_state_update_callback,     \
                                  zego_register_range_scene_team_team_state_update_callback)        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_range_scene_token_will_expire_callback,          \
                                  zego_register_range_scene_token_will_expire_callback)             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_range_scene_user_command_update_callback,        \
                                  zego_register_range_scene_user_command_update_callback)           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_range_scene_user_status_update_callback,         \
                                  zego_register_range_scene_user_status_update_callback)            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_real_time_sequential_data_sent_callback,         \
                                  zego_register_real_time_sequential_data_sent_callback)            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_receive_real_time_sequential_data_callback,      \
                                  zego_register_receive_real_time_sequential_data_callback)         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_recv_experimental_api_callback,                  \
                                  zego_register_recv_experimental_api_callback)                     \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_recv_room_transparent_message_callback,          \
                                  zego_register_recv_room_transparent_message_callback)             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_recv_windows_message_callback,                   \
                                  zego_register_recv_windows_message_callback)                      \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_remote_audio_spectrum_update_callback,           \
                                  zego_register_remote_audio_spectrum_update_callback)              \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_remote_camera_state_update_callback,             \
                                  zego_register_remote_camera_state_update_callback)                \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_remote_mic_state_update_callback,                \
                                  zego_register_remote_mic_state_update_callback)                   \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_remote_sound_level_info_update_callback,         \
                                  zego_register_remote_sound_level_info_update_callback)            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_remote_sound_level_update_callback,              \
                                  zego_register_remote_sound_level_update_callback)                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_remote_speaker_state_update_callback,            \
                                  zego_register_remote_speaker_state_update_callback)               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_request_dump_data_callback,                      \
                                  zego_register_request_dump_data_callback)                         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_request_upload_dump_data_callback,               \
                                  zego_register_request_upload_dump_data_callback)                  \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_room_extra_info_update_callback,                 \
                                  zego_register_room_extra_info_update_callback)                    \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_room_login_result_callback,                      \
                                  zego_register_room_login_result_callback)                         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_room_logout_result_callback,                     \
                                  zego_register_room_logout_result_callback)                        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_room_online_user_count_update_callback,          \
                                  zego_register_room_online_user_count_update_callback)             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_room_set_room_extra_info_result_callback,        \
                                  zego_register_room_set_room_extra_info_result_callback)           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_room_state_changed_callback,                     \
                                  zego_register_room_state_changed_callback)                        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_room_state_update_callback,                      \
                                  zego_register_room_state_update_callback)                         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_room_stream_extra_info_update_callback,          \
                                  zego_register_room_stream_extra_info_update_callback)             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_room_stream_update_callback,                     \
                                  zego_register_room_stream_update_callback)                        \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_room_token_will_expire_callback,                 \
                                  zego_register_room_token_will_expire_callback)                    \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_room_user_update_callback,                       \
                                  zego_register_room_user_update_callback)                          \
            ZEGOEXP_LOAD_FUNC_PTR(                                                                  \
                pfnzego_register_screen_capture_mobile_exception_occurred_callback,                 \
                zego_register_screen_capture_mobile_exception_occurred_callback)                    \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_screen_capture_mobile_start_callback,            \
                                  zego_register_screen_capture_mobile_start_callback)               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_screen_capture_rect_changed_callback,            \
                                  zego_register_screen_capture_rect_changed_callback)               \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_screen_capture_source_available_frame_callback,  \
                                  zego_register_screen_capture_source_available_frame_callback)     \
            ZEGOEXP_LOAD_FUNC_PTR(                                                                  \
                pfnzego_register_screen_capture_source_exception_occurred_callback,                 \
                zego_register_screen_capture_source_exception_occurred_callback)                    \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_screen_capture_window_state_changed_callback,    \
                                  zego_register_screen_capture_window_state_changed_callback)       \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_send_transparent_message_result_callback,        \
                                  zego_register_send_transparent_message_result_callback)           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_start_dump_data_callback,                        \
                                  zego_register_start_dump_data_callback)                           \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_stop_dump_data_callback,                         \
                                  zego_register_stop_dump_data_callback)                            \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_test_network_connectivity_callback,              \
                                  zego_register_test_network_connectivity_callback)                 \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_unity_surface_texture_created_callback,          \
                                  zego_register_unity_surface_texture_created_callback)             \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_upload_dump_data_callback,                       \
                                  zego_register_upload_dump_data_callback)                          \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_upload_log_result_callback,                      \
                                  zego_register_upload_log_result_callback)                         \
            ZEGOEXP_LOAD_FUNC_PTR(pfnzego_register_video_device_state_changed_callback,             \
                                  zego_register_video_device_state_changed_callback)                \
            ZEGOEXP_LOAD_FUNC_PTR(                                                                  \
                pfnzego_register_video_object_segmentation_state_changed_callback,                  \
                zego_register_video_object_segmentation_state_changed_callback)                     \
        } while (false);                                                                            \
        return 0;                                                                                   \
    }                                                                                               \
    void unLoadLibraryInternal() {                                                                  \
        if (nullptr == handle) {                                                                    \
            return;                                                                                 \
        }                                                                                           \
        ZEGOEXP_FREE_LIBRARY()                                                                      \
        handle = nullptr;                                                                           \
    }

#endif // ZEGOEXP_EXPLICIT
#endif // __ZEGOEXPRESSEXPLICIT_HPP__
