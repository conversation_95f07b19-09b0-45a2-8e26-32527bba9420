#ifndef __ZegoServerAssistant__
#define __ZegoServerAssistant__

#include <stdint.h>
#include <map>
#include <string>

#include "ZegoServerAssistantDefines.h"

class ZEGOSA_API ZegoServerAssistant {
public:
    /**
     * @brief 获取 token 的静态方法
     *
     * @param appID Zego派发的数字ID, 各个开发者的唯一标识
     * @param userID 用户ID
     * @param secret 在鉴权 token 计算过程中 AES 加密需要的密钥,32字节的字符串
     * @param effectiveTimeInSeconds token 的有效时长，单位：秒
     * @return ZegoTokenResult
     */
    static ZegoToken04Result GenerateToken04(uint32_t appID, const std::string& userID, const std::string& secret, int64_t effectiveTimeInSeconds, const std::string& payload);
};

extern "C" {
    ZegoToken04Result GenerateToken04(uint32_t appID, const char* userID, const char* secret, int64_t effectiveTimeInSeconds, const char* payload);
}

#endif  // __ZegoServerAssistant__
