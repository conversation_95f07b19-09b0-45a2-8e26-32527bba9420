[package]
name = "zego_stream_gateway"
version = "0.1.0"
edition = "2021"

[dependencies]
nix = { version = "0.29", features = ["process"] }
libc = "0.2.161"
chrono = "0.4.38"
prost = "0.13.3"
tonic = { version = "0.12.3", features = ["transport", "server"] }
tokio = { version = "1.41.0", features = ["full"] }
tokio-stream = "0.1.16"
ipc-channel = "0.19.0"
async-std = "1.13.0"
serde = { version = "1.0.214", features = ["derive"] }
serde_json = "1.0.132"
lazy_static = "1.5.0"
md5 = "0.7.0"

[build-dependencies]
tonic-build = "0.12.3"

[[bin]]
name="zego_stream_gateway"
path="src/main.rs"

[[bin]]
name="zego_test"
path="src/zego_process/zego_test.rs"