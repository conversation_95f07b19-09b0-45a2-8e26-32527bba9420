// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v5.27.3
// source: proto/zego_stream.proto

package zego_stream

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// ZegoStreamClient is the client API for ZegoStream service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ZegoStreamClient interface {
	StreamTransfer(ctx context.Context, opts ...grpc.CallOption) (ZegoStream_StreamTransferClient, error)
}

type zegoStreamClient struct {
	cc grpc.ClientConnInterface
}

func NewZegoStreamClient(cc grpc.ClientConnInterface) ZegoStreamClient {
	return &zegoStreamClient{cc}
}

func (c *zegoStreamClient) StreamTransfer(ctx context.Context, opts ...grpc.CallOption) (ZegoStream_StreamTransferClient, error) {
	stream, err := c.cc.NewStream(ctx, &ZegoStream_ServiceDesc.Streams[0], "/zego_stream.ZegoStream/StreamTransfer", opts...)
	if err != nil {
		return nil, err
	}
	x := &zegoStreamStreamTransferClient{stream}
	return x, nil
}

type ZegoStream_StreamTransferClient interface {
	Send(*EventWrap) error
	Recv() (*EventWrap, error)
	grpc.ClientStream
}

type zegoStreamStreamTransferClient struct {
	grpc.ClientStream
}

func (x *zegoStreamStreamTransferClient) Send(m *EventWrap) error {
	return x.ClientStream.SendMsg(m)
}

func (x *zegoStreamStreamTransferClient) Recv() (*EventWrap, error) {
	m := new(EventWrap)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// ZegoStreamServer is the server API for ZegoStream service.
// All implementations must embed UnimplementedZegoStreamServer
// for forward compatibility
type ZegoStreamServer interface {
	StreamTransfer(ZegoStream_StreamTransferServer) error
	mustEmbedUnimplementedZegoStreamServer()
}

// UnimplementedZegoStreamServer must be embedded to have forward compatible implementations.
type UnimplementedZegoStreamServer struct {
}

func (UnimplementedZegoStreamServer) StreamTransfer(ZegoStream_StreamTransferServer) error {
	return status.Errorf(codes.Unimplemented, "method StreamTransfer not implemented")
}
func (UnimplementedZegoStreamServer) mustEmbedUnimplementedZegoStreamServer() {}

// UnsafeZegoStreamServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ZegoStreamServer will
// result in compilation errors.
type UnsafeZegoStreamServer interface {
	mustEmbedUnimplementedZegoStreamServer()
}

func RegisterZegoStreamServer(s grpc.ServiceRegistrar, srv ZegoStreamServer) {
	s.RegisterService(&ZegoStream_ServiceDesc, srv)
}

func _ZegoStream_StreamTransfer_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(ZegoStreamServer).StreamTransfer(&zegoStreamStreamTransferServer{stream})
}

type ZegoStream_StreamTransferServer interface {
	Send(*EventWrap) error
	Recv() (*EventWrap, error)
	grpc.ServerStream
}

type zegoStreamStreamTransferServer struct {
	grpc.ServerStream
}

func (x *zegoStreamStreamTransferServer) Send(m *EventWrap) error {
	return x.ServerStream.SendMsg(m)
}

func (x *zegoStreamStreamTransferServer) Recv() (*EventWrap, error) {
	m := new(EventWrap)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// ZegoStream_ServiceDesc is the grpc.ServiceDesc for ZegoStream service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ZegoStream_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "zego_stream.ZegoStream",
	HandlerType: (*ZegoStreamServer)(nil),
	Methods:     []grpc.MethodDesc{},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "StreamTransfer",
			Handler:       _ZegoStream_StreamTransfer_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "proto/zego_stream.proto",
}
