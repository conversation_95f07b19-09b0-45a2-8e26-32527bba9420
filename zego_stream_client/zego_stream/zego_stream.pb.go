// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v5.27.3
// source: proto/zego_stream.proto

package zego_stream

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type EventType int32

const (
	EventType_EventTypeNone EventType = 0
	// common: 1-10
	EventType_ZegoError   EventType = 1
	EventType_ServerError EventType = 2
	EventType_ClientError EventType = 3
	EventType_Audio       EventType = 4
	// client action: 11-99
	EventType_Exit            EventType = 10 // exit room
	EventType_Login           EventType = 11 // login room
	EventType_StartPullStream EventType = 12 // start pull stream
	EventType_StopPullStream  EventType = 13 // stop pull stream
	EventType_SendTextMessage EventType = 14 // send broadcast message
	// zego callback: 100-199
	EventType_LoginResult  EventType = 101 // login result
	EventType_StreamUpdate EventType = 102 // stream update
)

// Enum value maps for EventType.
var (
	EventType_name = map[int32]string{
		0:   "EventTypeNone",
		1:   "ZegoError",
		2:   "ServerError",
		3:   "ClientError",
		4:   "Audio",
		10:  "Exit",
		11:  "Login",
		12:  "StartPullStream",
		13:  "StopPullStream",
		14:  "SendTextMessage",
		101: "LoginResult",
		102: "StreamUpdate",
	}
	EventType_value = map[string]int32{
		"EventTypeNone":   0,
		"ZegoError":       1,
		"ServerError":     2,
		"ClientError":     3,
		"Audio":           4,
		"Exit":            10,
		"Login":           11,
		"StartPullStream": 12,
		"StopPullStream":  13,
		"SendTextMessage": 14,
		"LoginResult":     101,
		"StreamUpdate":    102,
	}
)

func (x EventType) Enum() *EventType {
	p := new(EventType)
	*p = x
	return p
}

func (x EventType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EventType) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_zego_stream_proto_enumTypes[0].Descriptor()
}

func (EventType) Type() protoreflect.EnumType {
	return &file_proto_zego_stream_proto_enumTypes[0]
}

func (x EventType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EventType.Descriptor instead.
func (EventType) EnumDescriptor() ([]byte, []int) {
	return file_proto_zego_stream_proto_rawDescGZIP(), []int{0}
}

type EventWrap struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Event EventType `protobuf:"varint,1,opt,name=event,proto3,enum=zego_stream.EventType" json:"event,omitempty"`
	Json  string    `protobuf:"bytes,2,opt,name=json,proto3" json:"json,omitempty"`
	Data  []byte    `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *EventWrap) Reset() {
	*x = EventWrap{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_zego_stream_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EventWrap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EventWrap) ProtoMessage() {}

func (x *EventWrap) ProtoReflect() protoreflect.Message {
	mi := &file_proto_zego_stream_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EventWrap.ProtoReflect.Descriptor instead.
func (*EventWrap) Descriptor() ([]byte, []int) {
	return file_proto_zego_stream_proto_rawDescGZIP(), []int{0}
}

func (x *EventWrap) GetEvent() EventType {
	if x != nil {
		return x.Event
	}
	return EventType_EventTypeNone
}

func (x *EventWrap) GetJson() string {
	if x != nil {
		return x.Json
	}
	return ""
}

func (x *EventWrap) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_proto_zego_stream_proto protoreflect.FileDescriptor

var file_proto_zego_stream_proto_rawDesc = []byte{
	0x0a, 0x17, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x7a, 0x65, 0x67, 0x6f, 0x5f, 0x73, 0x74, 0x72,
	0x65, 0x61, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x7a, 0x65, 0x67, 0x6f, 0x5f,
	0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x22, 0x61, 0x0a, 0x09, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x57,
	0x72, 0x61, 0x70, 0x12, 0x2c, 0x0a, 0x05, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x16, 0x2e, 0x7a, 0x65, 0x67, 0x6f, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d,
	0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x05, 0x65, 0x76, 0x65, 0x6e,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x6a, 0x73, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6a, 0x73, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x2a, 0xd0, 0x01, 0x0a, 0x09, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x11, 0x0a, 0x0d, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x5a, 0x65,
	0x67, 0x6f, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x02, 0x12, 0x0f, 0x0a, 0x0b, 0x43, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x10, 0x03, 0x12, 0x09, 0x0a, 0x05, 0x41,
	0x75, 0x64, 0x69, 0x6f, 0x10, 0x04, 0x12, 0x08, 0x0a, 0x04, 0x45, 0x78, 0x69, 0x74, 0x10, 0x0a,
	0x12, 0x09, 0x0a, 0x05, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x10, 0x0b, 0x12, 0x13, 0x0a, 0x0f, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x50, 0x75, 0x6c, 0x6c, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x10, 0x0c,
	0x12, 0x12, 0x0a, 0x0e, 0x53, 0x74, 0x6f, 0x70, 0x50, 0x75, 0x6c, 0x6c, 0x53, 0x74, 0x72, 0x65,
	0x61, 0x6d, 0x10, 0x0d, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x65, 0x6e, 0x64, 0x54, 0x65, 0x78, 0x74,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x10, 0x0e, 0x12, 0x0f, 0x0a, 0x0b, 0x4c, 0x6f, 0x67,
	0x69, 0x6e, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x10, 0x65, 0x12, 0x10, 0x0a, 0x0c, 0x53, 0x74,
	0x72, 0x65, 0x61, 0x6d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x10, 0x66, 0x32, 0x52, 0x0a, 0x0a,
	0x5a, 0x65, 0x67, 0x6f, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x12, 0x44, 0x0a, 0x0e, 0x53, 0x74,
	0x72, 0x65, 0x61, 0x6d, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x12, 0x16, 0x2e, 0x7a,
	0x65, 0x67, 0x6f, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x57, 0x72, 0x61, 0x70, 0x1a, 0x16, 0x2e, 0x7a, 0x65, 0x67, 0x6f, 0x5f, 0x73, 0x74, 0x72, 0x65,
	0x61, 0x6d, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x57, 0x72, 0x61, 0x70, 0x28, 0x01, 0x30, 0x01,
	0x42, 0x0f, 0x5a, 0x0d, 0x2e, 0x2f, 0x7a, 0x65, 0x67, 0x6f, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x61,
	0x6d, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_zego_stream_proto_rawDescOnce sync.Once
	file_proto_zego_stream_proto_rawDescData = file_proto_zego_stream_proto_rawDesc
)

func file_proto_zego_stream_proto_rawDescGZIP() []byte {
	file_proto_zego_stream_proto_rawDescOnce.Do(func() {
		file_proto_zego_stream_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_zego_stream_proto_rawDescData)
	})
	return file_proto_zego_stream_proto_rawDescData
}

var file_proto_zego_stream_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_proto_zego_stream_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_proto_zego_stream_proto_goTypes = []interface{}{
	(EventType)(0),    // 0: zego_stream.EventType
	(*EventWrap)(nil), // 1: zego_stream.EventWrap
}
var file_proto_zego_stream_proto_depIdxs = []int32{
	0, // 0: zego_stream.EventWrap.event:type_name -> zego_stream.EventType
	1, // 1: zego_stream.ZegoStream.StreamTransfer:input_type -> zego_stream.EventWrap
	1, // 2: zego_stream.ZegoStream.StreamTransfer:output_type -> zego_stream.EventWrap
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_proto_zego_stream_proto_init() }
func file_proto_zego_stream_proto_init() {
	if File_proto_zego_stream_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_zego_stream_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EventWrap); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_zego_stream_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_zego_stream_proto_goTypes,
		DependencyIndexes: file_proto_zego_stream_proto_depIdxs,
		EnumInfos:         file_proto_zego_stream_proto_enumTypes,
		MessageInfos:      file_proto_zego_stream_proto_msgTypes,
	}.Build()
	File_proto_zego_stream_proto = out.File
	file_proto_zego_stream_proto_rawDesc = nil
	file_proto_zego_stream_proto_goTypes = nil
	file_proto_zego_stream_proto_depIdxs = nil
}
