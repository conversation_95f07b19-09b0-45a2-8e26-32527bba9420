package main

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"os"
	"strconv"
	"strings"
	"sync"
	"time"

	"gitlab.ttyuyin.com/mp-engineering/zego_stream_gateway/zego_stream_client/zego_stream"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

const (
	maxRecvSize        = 1024 * 1024 * 100 // 100M
	benchAppId         = 1001672063
	sendAudioBatchSize = 32000 / 10
	sendAudioInterval  = 100 * time.Millisecond
)

var (
	serverAddr = "localhost:8000"

	demoAudioData []byte
)

func init() {
	data, err := os.ReadFile("test.wav")
	if err != nil {
		panic(err)
	}
	demoAudioData = data[44:]
}

func timeNowMs() string {
	return time.Now().Format("15:04:05.000")
}

type ZegoStreamClient struct {
	appId    uint32
	roomId   string
	userId   string
	streamId string

	launchTime    time.Time
	loginTime     time.Time
	startPullTime time.Time
	// firstRecvTime time.Time

	group  *ZegoStreamBenchGroup
	conn   *grpc.ClientConn
	stream zego_stream.ZegoStream_StreamTransferClient
}

type ZegoStreamBenchStats struct {
	waitgroup          sync.WaitGroup
	sendFirstAudioTime time.Time
	sendLastAudioTime  time.Time
	recvFirstAudioTime time.Time
	recvLastAudioTime  time.Time
}

type ZegoStreamBenchGroup struct {
	ctx context.Context

	groupId int
	appId   uint32
	roomId  string

	sendUserId string
	recvUserId string

	sendStreamId string
	recvStreamId string

	sendAudioCli *ZegoStreamClient
	recvAudioCli *ZegoStreamClient

	ZegoStreamBenchStats

	fatal error
}

func NewZegoStreamBenchGroup(ctx context.Context, appId uint32, groupId int) (*ZegoStreamBenchGroup, error) {
	roomId := fmt.Sprintf("%d", groupId+90000)
	sendUserId, recvUserId := fmt.Sprintf("%d", groupId+91000), fmt.Sprintf("%d", groupId+92000)
	sendStreamId, recvStreamId := fmt.Sprintf("%d", groupId+91000), fmt.Sprintf("%d", groupId+92000)

	group := &ZegoStreamBenchGroup{
		ctx: ctx, groupId: groupId, appId: appId, roomId: roomId,
		sendUserId: sendUserId, recvUserId: recvUserId,
		sendStreamId: sendStreamId, recvStreamId: recvStreamId,
	}

	if err := group.connect(ctx); err != nil {
		return nil, fmt.Errorf("group %d connect failed: %w", groupId, err)
	}

	return group, nil
}

func (group *ZegoStreamBenchGroup) connect(ctx context.Context) error {
	sendAudioCli, err := NewZegoStreamClient(ctx)
	if err != nil {
		return err
	}
	group.sendAudioCli = sendAudioCli
	group.sendAudioCli.SetBenchGroup(group)
	group.sendAudioCli.loginRoom(group.appId, group.roomId, group.sendUserId, group.sendStreamId)

	time.Sleep(100 * time.Millisecond)

	recvAudioCli, err := NewZegoStreamClient(ctx)
	if err != nil {
		return err
	}
	group.recvAudioCli = recvAudioCli
	group.recvAudioCli.SetBenchGroup(group)
	group.recvAudioCli.loginRoom(group.appId, group.roomId, group.recvUserId, group.recvStreamId)

	fmt.Printf("%s group %d login done: %s send: %s , %s recv: %s\n", timeNowMs(), group.groupId,
		group.sendUserId, group.sendAudioCli.loginTime.Sub(group.sendAudioCli.launchTime).Truncate(time.Millisecond),
		group.recvUserId, group.recvAudioCli.loginTime.Sub(group.recvAudioCli.launchTime).Truncate(time.Millisecond),
	)

	return nil
}

func (group *ZegoStreamBenchGroup) Close() error {
	group.sendAudioCli.Close()
	group.recvAudioCli.Close()
	return nil
}

func (group *ZegoStreamBenchGroup) resetStats() {
	group.sendFirstAudioTime = time.Time{}
	group.sendLastAudioTime = time.Time{}
	group.recvFirstAudioTime = time.Time{}
	group.recvLastAudioTime = time.Time{}
}

func (group *ZegoStreamBenchGroup) Loop(ctx context.Context, count int) {
	for i := 1; i < count+1; i++ {
		group.resetStats()
		// fmt.Printf("%s group %d room %s round %d start bench\n", timeNowMs(), group.groupId, group.roomId, i)
		group.waitgroup.Add(2)
		go group.recvAudioCli.recvEvent()
		go group.sendAudioCli.sendEvent()
		group.waitgroup.Wait()

		if group.fatal != nil {
			fmt.Printf("%s group %d room %s round %d bench failed: %s\n", timeNowMs(), group.groupId, group.roomId, i, group.fatal)
			group.Close()
			time.Sleep(100 * time.Millisecond)
			if err := group.connect(ctx); err != nil {
				fmt.Printf("%s group %d room %s round %d reconnect failed: %s\n", timeNowMs(), group.groupId, group.roomId, i, err)
				break
			}
			group.fatal = nil
			continue
		}

		fmt.Printf("%s group %03d room %s round %d bench done, start-delay: %s, end-delay: %s send-du: %s, recv-du: %s \n", timeNowMs(), group.groupId, group.roomId, i,
			group.recvFirstAudioTime.Sub(group.sendFirstAudioTime).Truncate(time.Millisecond),
			group.recvLastAudioTime.Sub(group.sendLastAudioTime).Truncate(time.Millisecond),
			group.sendLastAudioTime.Sub(group.sendFirstAudioTime).Truncate(time.Millisecond),
			group.recvLastAudioTime.Sub(group.recvFirstAudioTime).Truncate(time.Millisecond),
		)
	}
}

func NewZegoStreamClient(ctx context.Context) (*ZegoStreamClient, error) {
	launchTime := time.Now()
	conn, err := grpc.NewClient(serverAddr, grpc.WithTransportCredentials(insecure.NewCredentials()), grpc.WithDefaultCallOptions(grpc.MaxCallRecvMsgSize(maxRecvSize)))
	if err != nil {
		return nil, err
	}
	stream, err := zego_stream.NewZegoStreamClient(conn).StreamTransfer(ctx)
	if err != nil {
		return nil, err
	}
	// fmt.Println(timeNowMs(), "zego stream client ready")
	return &ZegoStreamClient{launchTime: launchTime, stream: stream, conn: conn}, nil
}

func (cli *ZegoStreamClient) SetBenchGroup(group *ZegoStreamBenchGroup) {
	cli.group = group
}

func (cli *ZegoStreamClient) Close() error {
	cli.stream.CloseSend()
	return cli.conn.Close()
}

func (cli *ZegoStreamClient) loginRoom(appID uint32, roomID, userID, streamID string) error {
	cli.appId, cli.roomId, cli.userId, cli.streamId = appID, roomID, userID, streamID

	// fmt.Println(timeNowMs(), "user", userID, "login room", roomID)
	jsonRaw, err := json.Marshal(map[string]any{"app_id": appID, "room_id": roomID, "user_id": userID, "stream_id": streamID})
	if err != nil {
		return err
	}
	err = cli.stream.Send(&zego_stream.EventWrap{Event: zego_stream.EventType_Login, Json: string(jsonRaw)})
	if err != nil {
		return err
	}

	return cli.waitLoginResult()
}

func (cli *ZegoStreamClient) waitLoginResult() error {
	for {
		msg, err := cli.stream.Recv()
		if err != nil {
			return err
		}
		if msg.Event == zego_stream.EventType_LoginResult {
			cli.loginTime = time.Now()
			// fmt.Println(timeNowMs(), "user", cli.userId, "login room", cli.roomId, "done, cost:", cli.loginTime.Sub(cli.launchTime))
			return nil
		} else {
			return fmt.Errorf("login failed: %s", msg.Json)
		}
	}
}

func (cli *ZegoStreamClient) sendTextMessage(content string) error {
	jsonRaw, err := json.Marshal(map[string]string{"content": content})
	if err != nil {
		return err
	}
	return cli.stream.Send(&zego_stream.EventWrap{Event: zego_stream.EventType_SendTextMessage, Json: string(jsonRaw)})
}

func (cli *ZegoStreamClient) startPullStream(streamID string) error {
	jsonRaw, err := json.Marshal(map[string]string{"stream_id": streamID})
	if err != nil {
		return err
	}
	return cli.stream.Send(&zego_stream.EventWrap{Event: zego_stream.EventType_StartPullStream, Json: string(jsonRaw)})
}

func (cli *ZegoStreamClient) stopPullStream(streamID string) error {
	jsonRaw, err := json.Marshal(map[string]string{"stream_id": streamID})
	if err != nil {
		return err
	}
	return cli.stream.Send(&zego_stream.EventWrap{Event: zego_stream.EventType_StopPullStream, Json: string(jsonRaw)})
}

func (cli *ZegoStreamClient) sendAudioData(data []byte) error {
	for i := 0; i < len(data); i += sendAudioBatchSize {
		end := i + sendAudioBatchSize
		if end > len(data) {
			end = len(data)
		}
		cli.stream.Send(&zego_stream.EventWrap{Event: zego_stream.EventType_Audio, Data: data[i:end]})
		time.Sleep(sendAudioInterval)
	}
	return nil
}

func (cli *ZegoStreamClient) sendEvent() {
	defer cli.group.waitgroup.Done()

	cli.group.sendFirstAudioTime = time.Now()
	// fmt.Printf("%s user %s send first audio\n", timeNowMs(), cli.userId)
	cli.sendAudioData(demoAudioData)
	// fmt.Printf("%s user %s send last audio\n", timeNowMs(), cli.userId)
	cli.group.sendLastAudioTime = time.Now()
}

type streamUpdateEvent struct {
	RoomId     string   `json:"room_id"`
	UpdateType string   `json:"update_type"`
	StreamList []string `json:"stream_list"`
}

func (cli *ZegoStreamClient) keepRecvStreamEvent(ch chan *zego_stream.EventWrap) {
	defer close(ch)
	errCnt, errMsg := 0, ""
	for {
		msg, err := cli.stream.Recv()
		if err != nil {
			if errors.Is(err, io.EOF) {
				break
			}
			errCnt++
			if errMsg != err.Error() && errMsg != "" {
				fmt.Println(timeNowMs(), "user", cli.userId, "recv stream event error:", errMsg)
			}
			errMsg = err.Error()
			if errCnt > 10 {
				cli.stream.CloseSend()
				cli.conn.Close()

				cli.launchTime = time.Now()
				conn, err := grpc.NewClient(serverAddr, grpc.WithTransportCredentials(insecure.NewCredentials()), grpc.WithDefaultCallOptions(grpc.MaxCallRecvMsgSize(maxRecvSize)))
				if err != nil {
					cli.group.fatal = err
					break
				}
				stream, err := zego_stream.NewZegoStreamClient(conn).StreamTransfer(cli.group.ctx)
				if err != nil {
					cli.group.fatal = err
					break
				}
				err = cli.loginRoom(cli.group.appId, cli.group.roomId, cli.group.sendUserId, cli.group.sendStreamId)
				if err != nil {
					cli.group.fatal = err
					break
				}

				cli.conn, cli.stream = conn, stream
			}
			continue
		}
		errCnt, errMsg = 0, ""
		ch <- msg
	}
}

func (cli *ZegoStreamClient) recvEvent() {
	defer cli.group.waitgroup.Done()

	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	ch := make(chan *zego_stream.EventWrap)
	go cli.keepRecvStreamEvent(ch)

	pullStream := ""
	emptyAudio := 0

	var keepLoop = true
	for keepLoop {
		select {
		case <-ticker.C:
			if !cli.group.recvLastAudioTime.IsZero() && emptyAudio > 50 {
				keepLoop = false
			}

		case msg, ok := <-ch:
			if !ok {
				keepLoop = false
				break
			}
			switch msg.Event {
			case zego_stream.EventType_LoginResult:
				cli.loginTime = time.Now()
				fmt.Printf("%s group %d user %s relogin, cost: %s\n", timeNowMs(), cli.group.groupId, cli.userId, cli.loginTime.Sub(cli.launchTime))
			// 	fmt.Printf("%s recv login result, cost: %s\n", cli.userId, cli.loginTime.Sub(cli.launchTime))
			case zego_stream.EventType_StreamUpdate:
				event := new(streamUpdateEvent)
				json.Unmarshal([]byte(msg.Json), event)
				if event.UpdateType == "add" {
					for _, streamId := range event.StreamList {
						if strings.HasPrefix(streamId, "91") {
							cli.startPullTime = time.Now()
							cli.startPullStream(streamId)
							pullStream = streamId
							// fmt.Printf("%s user %s start pull stream: %s\n", timeNowMs(), cli.userId, streamId)
						}
					}
				}

			case zego_stream.EventType_Audio:
				if sumAudioData(msg.Data) > 0 {
					if cli.group.recvFirstAudioTime.IsZero() {
						cli.group.recvFirstAudioTime = time.Now()
						// fmt.Printf("%s user %s recv first audio\n", timeNowMs(), cli.userId)
					}
					cli.group.recvLastAudioTime = time.Now()
					emptyAudio = 0
				} else {
					emptyAudio++
				}
				// fmt.Println(timeNowMs(), "empty audio:", emptyAudio)

			}
		}
	}

	if pullStream == "" {
		cli.stopPullStream(pullStream)
	}
}

var benchWaitGroup sync.WaitGroup

func bench(ctx context.Context, groupId int, count int) {
	benchWaitGroup.Add(1)
	group, err := NewZegoStreamBenchGroup(ctx, benchAppId, groupId)
	if err != nil {
		panic(err)
	}
	group.Loop(ctx, count)
	group.Close()
	benchWaitGroup.Done()
}

func sumAudioData(data []byte) int {
	sum := 0
	for _, v := range data {
		sum += int(v)
	}
	return sum
}

func getParams() (int, int, string) {
	concurrent, count, addr := 1, 1, serverAddr
	if len(os.Args) > 1 {
		concurrent, _ = strconv.Atoi(os.Args[1])
	}
	if len(os.Args) > 2 {
		count, _ = strconv.Atoi(os.Args[2])
	}
	if len(os.Args) > 3 {
		addr = os.Args[3]
	}

	if concurrent <= 0 {
		concurrent = 1
	}
	if count <= 0 {
		count = 1
	}
	if len(addr) == 0 {
		addr = serverAddr
	}

	return concurrent, count, addr
}

func main() {
	concurrent, count, addr := getParams()
	serverAddr = addr

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	fmt.Printf("%s start bench %s, concurrent: %d, count: %d\n", timeNowMs(), addr, concurrent, count)
	for i := 1; i < concurrent+1; i++ {
		go bench(ctx, i, count)
		time.Sleep(250 * time.Millisecond)
	}
	benchWaitGroup.Wait()
	fmt.Printf("%s all bench done\n", timeNowMs())
	time.Sleep(2 * time.Second)
}
