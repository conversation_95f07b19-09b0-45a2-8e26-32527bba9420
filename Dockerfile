FROM cr.ttyuyin.com/zt/rust:1.82.0 as builder

RUN sed -i 's|http://deb.debian.org|http://mirrors.tuna.tsinghua.edu.cn|g' /etc/apt/sources.list.d/debian.sources
RUN apt update
RUN apt install -y protobuf-compiler
R<PERSON> apt install -y alsa-utils
RUN apt install -y v4l-utils

WORKDIR /aigc/zego_stream_gateway
COPY realtime_chat/zego_stream_gateway/Cargo.toml realtime_chat/zego_stream_gateway/Cargo.lock /aigc/zego_stream_gateway/
RUN mkdir -p /aigc/zego_stream_gateway/src
RUN echo "fn main() {}" > /aigc/zego_stream_gateway/src/main.rs
RUN cargo build --release --bin zego_stream_gateway

ENV LD_LIBRARY_PATH=libs
COPY realtime_chat/zego_stream_gateway/libs/ /aigc/zego_stream_gateway/libs
COPY realtime_chat/zego_stream_gateway/proto/ /aigc/zego_stream_gateway/proto
COPY realtime_chat/zego_stream_gateway/build.rs /aigc/zego_stream_gateway/
COPY realtime_chat/zego_stream_gateway/src/ /aigc/zego_stream_gateway/src
RUN cargo build --release --bin zego_stream_gateway

FROM cr.ttyuyin.com/zt/debian:12.7 as runner

RUN sed -i 's|http://deb.debian.org|http://mirrors.tuna.tsinghua.edu.cn|g' /etc/apt/sources.list.d/debian.sources
RUN apt update
RUN apt install -y alsa-utils
RUN apt install -y v4l-utils

WORKDIR /app
ENV LD_LIBRARY_PATH=/app/libs
COPY realtime_chat/zego_stream_gateway/libs/ /app/libs
COPY --from=builder /aigc/zego_stream_gateway/target/release/zego_stream_gateway /app/zego-stream-gateway
CMD ["/app/zego-stream-gateway"]
