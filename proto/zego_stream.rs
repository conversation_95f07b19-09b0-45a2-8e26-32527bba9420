// This file is @generated by prost-build.
#[derive(<PERSON><PERSON>, PartialEq, ::prost::Message)]
pub struct EventWrap {
    #[prost(enumeration = "EventType", tag = "1")]
    pub event: i32,
    #[prost(string, tag = "2")]
    pub json: ::prost::alloc::string::String,
    #[prost(bytes = "vec", tag = "3")]
    pub data: ::prost::alloc::vec::Vec<u8>,
}
#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, PartialEq, Eq, Hash, PartialOrd, Ord, ::prost::Enumeration)]
#[repr(i32)]
pub enum EventType {
    None = 0,
    /// common: 1-10
    ZegoError = 1,
    ServerError = 2,
    ClientError = 3,
    Audio = 4,
    /// client action: 11-99
    ///
    /// exit room
    Exit = 10,
    /// login room
    Login = 11,
    /// start pull stream
    StartPullStream = 12,
    /// stop pull stream
    StopPullStream = 13,
    /// send broadcast message
    SendTextMessage = 14,
    /// zego callback: 100-199
    ///
    /// login result
    LoginResult = 101,
    /// stream update
    StreamUpdate = 102,
}
impl EventType {
    /// String value of the enum field names used in the ProtoBuf definition.
    ///
    /// The values are not transformed in any way and thus are considered stable
    /// (if the ProtoBuf definition does not change) and safe for programmatic use.
    pub fn as_str_name(&self) -> &'static str {
        match self {
            Self::None => "EventTypeNone",
            Self::ZegoError => "ZegoError",
            Self::ServerError => "ServerError",
            Self::ClientError => "ClientError",
            Self::Audio => "Audio",
            Self::Exit => "Exit",
            Self::Login => "Login",
            Self::StartPullStream => "StartPullStream",
            Self::StopPullStream => "StopPullStream",
            Self::SendTextMessage => "SendTextMessage",
            Self::LoginResult => "LoginResult",
            Self::StreamUpdate => "StreamUpdate",
        }
    }
    /// Creates an enum from field names used in the ProtoBuf definition.
    pub fn from_str_name(value: &str) -> ::core::option::Option<Self> {
        match value {
            "EventTypeNone" => Some(Self::None),
            "ZegoError" => Some(Self::ZegoError),
            "ServerError" => Some(Self::ServerError),
            "ClientError" => Some(Self::ClientError),
            "Audio" => Some(Self::Audio),
            "Exit" => Some(Self::Exit),
            "Login" => Some(Self::Login),
            "StartPullStream" => Some(Self::StartPullStream),
            "StopPullStream" => Some(Self::StopPullStream),
            "SendTextMessage" => Some(Self::SendTextMessage),
            "LoginResult" => Some(Self::LoginResult),
            "StreamUpdate" => Some(Self::StreamUpdate),
            _ => None,
        }
    }
}
/// Generated server implementations.
pub mod zego_stream_server {
    #![allow(
        unused_variables,
        dead_code,
        missing_docs,
        clippy::wildcard_imports,
        clippy::let_unit_value,
    )]
    use tonic::codegen::*;
    /// Generated trait containing gRPC methods that should be implemented for use with ZegoStreamServer.
    #[async_trait]
    pub trait ZegoStream: std::marker::Send + std::marker::Sync + 'static {
        /// Server streaming response type for the StreamTransfer method.
        type StreamTransferStream: tonic::codegen::tokio_stream::Stream<
                Item = std::result::Result<super::EventWrap, tonic::Status>,
            >
            + std::marker::Send
            + 'static;
        async fn stream_transfer(
            &self,
            request: tonic::Request<tonic::Streaming<super::EventWrap>>,
        ) -> std::result::Result<
            tonic::Response<Self::StreamTransferStream>,
            tonic::Status,
        >;
    }
    #[derive(Debug)]
    pub struct ZegoStreamServer<T> {
        inner: Arc<T>,
        accept_compression_encodings: EnabledCompressionEncodings,
        send_compression_encodings: EnabledCompressionEncodings,
        max_decoding_message_size: Option<usize>,
        max_encoding_message_size: Option<usize>,
    }
    impl<T> ZegoStreamServer<T> {
        pub fn new(inner: T) -> Self {
            Self::from_arc(Arc::new(inner))
        }
        pub fn from_arc(inner: Arc<T>) -> Self {
            Self {
                inner,
                accept_compression_encodings: Default::default(),
                send_compression_encodings: Default::default(),
                max_decoding_message_size: None,
                max_encoding_message_size: None,
            }
        }
        pub fn with_interceptor<F>(
            inner: T,
            interceptor: F,
        ) -> InterceptedService<Self, F>
        where
            F: tonic::service::Interceptor,
        {
            InterceptedService::new(Self::new(inner), interceptor)
        }
        /// Enable decompressing requests with the given encoding.
        #[must_use]
        pub fn accept_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.accept_compression_encodings.enable(encoding);
            self
        }
        /// Compress responses with the given encoding, if the client supports it.
        #[must_use]
        pub fn send_compressed(mut self, encoding: CompressionEncoding) -> Self {
            self.send_compression_encodings.enable(encoding);
            self
        }
        /// Limits the maximum size of a decoded message.
        ///
        /// Default: `4MB`
        #[must_use]
        pub fn max_decoding_message_size(mut self, limit: usize) -> Self {
            self.max_decoding_message_size = Some(limit);
            self
        }
        /// Limits the maximum size of an encoded message.
        ///
        /// Default: `usize::MAX`
        #[must_use]
        pub fn max_encoding_message_size(mut self, limit: usize) -> Self {
            self.max_encoding_message_size = Some(limit);
            self
        }
    }
    impl<T, B> tonic::codegen::Service<http::Request<B>> for ZegoStreamServer<T>
    where
        T: ZegoStream,
        B: Body + std::marker::Send + 'static,
        B::Error: Into<StdError> + std::marker::Send + 'static,
    {
        type Response = http::Response<tonic::body::BoxBody>;
        type Error = std::convert::Infallible;
        type Future = BoxFuture<Self::Response, Self::Error>;
        fn poll_ready(
            &mut self,
            _cx: &mut Context<'_>,
        ) -> Poll<std::result::Result<(), Self::Error>> {
            Poll::Ready(Ok(()))
        }
        fn call(&mut self, req: http::Request<B>) -> Self::Future {
            match req.uri().path() {
                "/zego_stream.ZegoStream/StreamTransfer" => {
                    #[allow(non_camel_case_types)]
                    struct StreamTransferSvc<T: ZegoStream>(pub Arc<T>);
                    impl<T: ZegoStream> tonic::server::StreamingService<super::EventWrap>
                    for StreamTransferSvc<T> {
                        type Response = super::EventWrap;
                        type ResponseStream = T::StreamTransferStream;
                        type Future = BoxFuture<
                            tonic::Response<Self::ResponseStream>,
                            tonic::Status,
                        >;
                        fn call(
                            &mut self,
                            request: tonic::Request<tonic::Streaming<super::EventWrap>>,
                        ) -> Self::Future {
                            let inner = Arc::clone(&self.0);
                            let fut = async move {
                                <T as ZegoStream>::stream_transfer(&inner, request).await
                            };
                            Box::pin(fut)
                        }
                    }
                    let accept_compression_encodings = self.accept_compression_encodings;
                    let send_compression_encodings = self.send_compression_encodings;
                    let max_decoding_message_size = self.max_decoding_message_size;
                    let max_encoding_message_size = self.max_encoding_message_size;
                    let inner = self.inner.clone();
                    let fut = async move {
                        let method = StreamTransferSvc(inner);
                        let codec = tonic::codec::ProstCodec::default();
                        let mut grpc = tonic::server::Grpc::new(codec)
                            .apply_compression_config(
                                accept_compression_encodings,
                                send_compression_encodings,
                            )
                            .apply_max_message_size_config(
                                max_decoding_message_size,
                                max_encoding_message_size,
                            );
                        let res = grpc.streaming(method, req).await;
                        Ok(res)
                    };
                    Box::pin(fut)
                }
                _ => {
                    Box::pin(async move {
                        let mut response = http::Response::new(empty_body());
                        let headers = response.headers_mut();
                        headers
                            .insert(
                                tonic::Status::GRPC_STATUS,
                                (tonic::Code::Unimplemented as i32).into(),
                            );
                        headers
                            .insert(
                                http::header::CONTENT_TYPE,
                                tonic::metadata::GRPC_CONTENT_TYPE,
                            );
                        Ok(response)
                    })
                }
            }
        }
    }
    impl<T> Clone for ZegoStreamServer<T> {
        fn clone(&self) -> Self {
            let inner = self.inner.clone();
            Self {
                inner,
                accept_compression_encodings: self.accept_compression_encodings,
                send_compression_encodings: self.send_compression_encodings,
                max_decoding_message_size: self.max_decoding_message_size,
                max_encoding_message_size: self.max_encoding_message_size,
            }
        }
    }
    /// Generated gRPC service name
    pub const SERVICE_NAME: &str = "zego_stream.ZegoStream";
    impl<T> tonic::server::NamedService for ZegoStreamServer<T> {
        const NAME: &'static str = SERVICE_NAME;
    }
}
