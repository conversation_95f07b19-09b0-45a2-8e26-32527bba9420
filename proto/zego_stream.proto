syntax = "proto3";

package zego_stream;

option go_package = "./zego_stream";

enum EventType {
  EventTypeNone    = 0;
  
  // common: 1-10
  ZegoError      = 1;
  ServerError    = 2;
  ClientError    = 3;
  Audio          = 4;

  // client action: 11-99
  Exit            = 10; // exit room
  Login           = 11; // login room
  StartPullStream = 12; // start pull stream
  StopPullStream  = 13; // stop pull stream
  SendTextMessage = 14; // send broadcast message

  // zego callback: 100-199
  LoginResult    = 101; // login result
  StreamUpdate   = 102; // stream update
}

message EventWrap {
  EventType  event = 1;
  string     json  = 2;
  bytes      data  = 3;
}

service ZegoStream {
  rpc StreamTransfer(stream EventWrap) returns (stream EventWrap);
}
